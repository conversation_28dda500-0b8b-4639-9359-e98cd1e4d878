/**
 * Mortgage/Loan Calculator Scripts
 */

document.addEventListener("DOMContentLoaded", function () {
  // Initialize EMI Calculator if it exists on the page
  const emiCalculatorForm = document.getElementById("emi-calculator-form");
  if (emiCalculatorForm) {
    initEMICalculator();
  }

  // Initialize Mortgage Calculator if it exists on the page
  const mortgageCalculatorForm = document.getElementById(
    "mortgage-calculator-form",
  );
  if (mortgageCalculatorForm) {
    initMortgageCalculator();
  }

  // Initialize Loan Affordability Calculator if it exists on the page
  const affordabilityForm = document.getElementById(
    "affordability-calculator-form",
  );
  if (affordabilityForm) {
    initAffordabilityCalculator();
  }

  // Initialize Loan Comparison Calculator if it exists on the page
  const comparisonForm = document.getElementById("comparison-calculator-form");
  if (comparisonForm) {
    initComparisonCalculator();
  }

  // Initialize Amortization Schedule Calculator if it exists on the page
  const amortizationForm = document.getElementById(
    "amortization-calculator-form",
  );
  if (amortizationForm) {
    initAmortizationCalculator();
  }

  // Initialize Prepayment Calculator if it exists on the page
  const prepaymentForm = document.getElementById("prepayment-calculator-form");
  if (prepaymentForm) {
    initPrepaymentCalculator();
  }
});

/**
 * Initialize EMI Calculator
 */
function initEMICalculator() {
  // Get form elements
  const form = document.getElementById("emi-calculator-form");
  const loanAmount = document.getElementById("loan-amount");
  const interestRate = document.getElementById("interest-rate");
  const loanTenure = document.getElementById("loan-tenure");
  const tenureType = document.getElementById("tenure-type");
  const results = document.getElementById("emi-results");

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("emi");
  if (savedValues) {
    loanAmount.value = savedValues.loanAmount;
    interestRate.value = savedValues.interestRate;
    loanTenure.value = savedValues.loanTenure;
    tenureType.value = savedValues.tenureType;
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const loanAmountValue = parseFloat(loanAmount.value);
    const interestRateValue = parseFloat(interestRate.value);
    const loanTenureValue = parseInt(loanTenure.value);
    const tenureTypeValue = tenureType.value;

    // Validate inputs
    const amountValidation = calculatorUtils.validateNumericInput(
      loanAmountValue,
      1000,
      Number.MAX_SAFE_INTEGER,
      "Please enter a valid loan amount (minimum ₹1,000)",
    );

    if (!amountValidation.valid) {
      calculatorUtils.showError(loanAmount, amountValidation.message);
      return;
    }

    const rateValidation = calculatorUtils.validateNumericInput(
      interestRateValue,
      1,
      50,
      "Please enter a valid interest rate between 1% and 50%",
    );

    if (!rateValidation.valid) {
      calculatorUtils.showError(interestRate, rateValidation.message);
      return;
    }

    const tenureValidation = calculatorUtils.validateNumericInput(
      loanTenureValue,
      1,
      tenureTypeValue === "year" ? 50 : 600,
      `Please enter a valid loan tenure between 1 and ${
        tenureTypeValue === "year" ? "50 years" : "600 months"
      }`,
    );

    if (!tenureValidation.valid) {
      calculatorUtils.showError(loanTenure, tenureValidation.message);
      return;
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("emi", {
      loanAmount: loanAmountValue,
      interestRate: interestRateValue,
      loanTenure: loanTenureValue,
      tenureType: tenureTypeValue,
    });

    // Calculate EMI
    // Convert tenure to months if in years
    const tenureInMonths =
      tenureTypeValue === "year" ? loanTenureValue * 12 : loanTenureValue;

    // Calculate monthly interest rate (annual rate / 12 / 100)
    const monthlyRate = interestRateValue / (12 * 100);

    // Calculate EMI using formula: EMI = [P × r × (1 + r)^n] ÷ [(1 + r)^n - 1]
    // Where P is principal, r is monthly rate, n is number of months
    const emi =
      (loanAmountValue *
        monthlyRate *
        Math.pow(1 + monthlyRate, tenureInMonths)) /
      (Math.pow(1 + monthlyRate, tenureInMonths) - 1);

    // Calculate total payment and total interest
    const totalPayment = emi * tenureInMonths;
    const totalInterest = totalPayment - loanAmountValue;

    // Round values to 2 decimal places
    const roundedEMI = calculatorUtils.round(emi, 2);
    const roundedTotalPayment = calculatorUtils.round(totalPayment, 2);
    const roundedTotalInterest = calculatorUtils.round(totalInterest, 2);

    // Display results
    document.getElementById("monthly-emi").textContent =
      calculatorUtils.formatCurrency(roundedEMI);
    document.getElementById("total-interest").textContent =
      calculatorUtils.formatCurrency(roundedTotalInterest);
    document.getElementById("total-payment").textContent =
      calculatorUtils.formatCurrency(roundedTotalPayment);
    results.style.display = "block";

    // Generate and display chart
    generateEmiChart(loanAmountValue, roundedTotalInterest);

    // Save calculation to history
    storageManager.saveCalculationHistory("emi", {
      loanAmount: loanAmountValue,
      interestRate: interestRateValue,
      loanTenure: loanTenureValue,
      tenureType: tenureTypeValue,
      emi: roundedEMI,
      totalInterest: roundedTotalInterest,
      totalPayment: roundedTotalPayment,
    });

    // Show in-article ad after calculation
    const inArticleAd = document.getElementById(
      "ad-between-calculator-results",
    );
    if (inArticleAd) {
      inArticleAd.style.display = "block";
    }
  });
}

/**
 * Generate EMI breakdown chart
 */
function generateEmiChart(principal, totalInterest) {
  const chartContainer = document.getElementById("emi-chart-container");
  if (!chartContainer) return;

  chartContainer.innerHTML = ""; // Clear previous chart

  // Create a simple pie chart using HTML/CSS
  const chartEl = document.createElement("div");
  chartEl.className = "pie-chart";

  const totalAmount = principal + totalInterest;
  const principalPercentage = (principal / totalAmount) * 100;
  const interestPercentage = (totalInterest / totalAmount) * 100;

  // Create chart legend
  const legendEl = document.createElement("div");
  legendEl.className = "chart-legend";

  const principalLegend = document.createElement("div");
  principalLegend.className = "legend-item";
  principalLegend.innerHTML = `<span class="legend-color" style="background-color: var(--primary-color);"></span>
                              <span class="legend-text">Principal: ${calculatorUtils.formatCurrency(
                                principal,
                              )} (${principalPercentage.toFixed(1)}%)</span>`;

  const interestLegend = document.createElement("div");
  interestLegend.className = "legend-item";
  interestLegend.innerHTML = `<span class="legend-color" style="background-color: var(--secondary-color);"></span>
                             <span class="legend-text">Interest: ${calculatorUtils.formatCurrency(
                               totalInterest,
                             )} (${interestPercentage.toFixed(1)}%)</span>`;

  legendEl.appendChild(principalLegend);
  legendEl.appendChild(interestLegend);

  // Create the actual pie chart
  const pieEl = document.createElement("div");
  pieEl.className = "pie";
  pieEl.style.background = `conic-gradient(
    var(--primary-color) 0% ${principalPercentage}%,
    var(--secondary-color) ${principalPercentage}% 100%
  )`;

  chartEl.appendChild(pieEl);
  chartContainer.appendChild(chartEl);
  chartContainer.appendChild(legendEl);
}

/**
 * Initialize Loan Affordability Calculator
 */
function initAffordabilityCalculator() {
  // Get form elements
  const form = document.getElementById("affordability-calculator-form");
  const monthlyIncome = document.getElementById("monthly-income");
  const monthlyExpenses = document.getElementById("monthly-expenses");
  const existingEMIs = document.getElementById("existing-emis");
  const interestRate = document.getElementById("interest-rate");
  const loanTenure = document.getElementById("loan-tenure");
  const tenureType = document.getElementById("tenure-type");
  const results = document.getElementById("affordability-results");

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("affordability");
  if (savedValues) {
    monthlyIncome.value = savedValues.monthlyIncome;
    monthlyExpenses.value = savedValues.monthlyExpenses;
    existingEMIs.value = savedValues.existingEMIs;
    interestRate.value = savedValues.interestRate;
    loanTenure.value = savedValues.loanTenure;
    tenureType.value = savedValues.tenureType;
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const monthlyIncomeValue = parseFloat(monthlyIncome.value);
    const monthlyExpensesValue = parseFloat(monthlyExpenses.value) || 0;
    const existingEMIsValue = parseFloat(existingEMIs.value) || 0;
    const interestRateValue = parseFloat(interestRate.value);
    const loanTenureValue = parseInt(loanTenure.value);
    const tenureTypeValue = tenureType.value;

    // Validate inputs
    const incomeValidation = calculatorUtils.validateNumericInput(
      monthlyIncomeValue,
      5000,
      Number.MAX_SAFE_INTEGER,
      "Please enter a valid monthly income (minimum ₹5,000)",
    );

    if (!incomeValidation.valid) {
      calculatorUtils.showError(monthlyIncome, incomeValidation.message);
      return;
    }

    const expensesValidation = calculatorUtils.validateNumericInput(
      monthlyExpensesValue,
      0,
      monthlyIncomeValue,
      "Expenses cannot exceed income",
    );

    if (!expensesValidation.valid) {
      calculatorUtils.showError(monthlyExpenses, expensesValidation.message);
      return;
    }

    // Existing EMIs validation removed as per user request

    const rateValidation = calculatorUtils.validateNumericInput(
      interestRateValue,
      1,
      50,
      "Please enter a valid interest rate between 1% and 50%",
    );

    if (!rateValidation.valid) {
      calculatorUtils.showError(interestRate, rateValidation.message);
      return;
    }

    const tenureValidation = calculatorUtils.validateNumericInput(
      loanTenureValue,
      1,
      tenureTypeValue === "year" ? 50 : 600,
      `Please enter a valid loan tenure between 1 and ${
        tenureTypeValue === "year" ? "50 years" : "600 months"
      }`,
    );

    if (!tenureValidation.valid) {
      calculatorUtils.showError(loanTenure, tenureValidation.message);
      return;
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("affordability", {
      monthlyIncome: monthlyIncomeValue,
      monthlyExpenses: monthlyExpensesValue,
      existingEMIs: existingEMIsValue,
      interestRate: interestRateValue,
      loanTenure: loanTenureValue,
      tenureType: tenureTypeValue,
    });

    // Calculate affordable loan amount
    // Assume 50% of income after expenses and existing EMIs can go towards new loan EMI
    const availableIncome =
      monthlyIncomeValue - monthlyExpensesValue - existingEMIsValue;
    const maxEMI = availableIncome * 0.5; // 50% of available income

    // Convert tenure to months if in years
    const tenureInMonths =
      tenureTypeValue === "year" ? loanTenureValue * 12 : loanTenureValue;

    // Calculate monthly interest rate (annual rate / 12 / 100)
    const monthlyRate = interestRateValue / (12 * 100);

    // Calculate affordable loan amount using EMI formula rearranged:
    // P = EMI × [(1 + r)^n - 1] ÷ [r × (1 + r)^n]
    // Where P is principal, r is monthly rate, n is number of months, EMI is maximum affordable EMI
    const affordableLoanAmount =
      maxEMI *
      ((Math.pow(1 + monthlyRate, tenureInMonths) - 1) /
        (monthlyRate * Math.pow(1 + monthlyRate, tenureInMonths)));

    // Round values to 2 decimal places
    const roundedAffordableLoanAmount = calculatorUtils.round(
      affordableLoanAmount,
      2,
    );
    const roundedMaxEMI = calculatorUtils.round(maxEMI, 2);

    // Display results
    document.getElementById("affordable-loan-amount").textContent =
      calculatorUtils.formatCurrency(roundedAffordableLoanAmount);
    document.getElementById("max-emi").textContent =
      calculatorUtils.formatCurrency(roundedMaxEMI);
    document.getElementById("available-income").textContent =
      calculatorUtils.formatCurrency(availableIncome);
    results.style.display = "block";

    // Save calculation to history
    storageManager.saveCalculationHistory("affordability", {
      monthlyIncome: monthlyIncomeValue,
      monthlyExpenses: monthlyExpensesValue,
      existingEMIs: existingEMIsValue,
      interestRate: interestRateValue,
      loanTenure: loanTenureValue,
      tenureType: tenureTypeValue,
      affordableLoanAmount: roundedAffordableLoanAmount,
      maxEMI: roundedMaxEMI,
      availableIncome: availableIncome,
    });

    // Show in-article ad after calculation
    const inArticleAd = document.getElementById(
      "ad-between-calculator-results",
    );
    if (inArticleAd) {
      inArticleAd.style.display = "block";
    }
  });
}

/**
 * Initialize Loan Comparison Calculator
 */
function initComparisonCalculator() {
  // Get form elements
  const form = document.getElementById("comparison-calculator-form");
  const loanAmount1 = document.getElementById("loan-amount-1");
  const interestRate1 = document.getElementById("interest-rate-1");
  const loanTenure1 = document.getElementById("loan-tenure-1");
  const processingFee1 = document.getElementById("processing-fee-1");

  const loanAmount2 = document.getElementById("loan-amount-2");
  const interestRate2 = document.getElementById("interest-rate-2");
  const loanTenure2 = document.getElementById("loan-tenure-2");
  const processingFee2 = document.getElementById("processing-fee-2");

  const results = document.getElementById("comparison-results");

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("loan-comparison");
  if (savedValues) {
    loanAmount1.value = savedValues.loanAmount1;
    interestRate1.value = savedValues.interestRate1;
    loanTenure1.value = savedValues.loanTenure1;
    processingFee1.value = savedValues.processingFee1;

    loanAmount2.value = savedValues.loanAmount2;
    interestRate2.value = savedValues.interestRate2;
    loanTenure2.value = savedValues.loanTenure2;
    processingFee2.value = savedValues.processingFee2;
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values for Loan 1
    const loanAmount1Value = parseFloat(loanAmount1.value);
    const interestRate1Value = parseFloat(interestRate1.value);
    const loanTenure1Value = parseInt(loanTenure1.value);
    const processingFee1Value = parseFloat(processingFee1.value) || 0;

    // Get input values for Loan 2
    const loanAmount2Value = parseFloat(loanAmount2.value);
    const interestRate2Value = parseFloat(interestRate2.value);
    const loanTenure2Value = parseInt(loanTenure2.value);
    const processingFee2Value = parseFloat(processingFee2.value) || 0;

    // Validate inputs for Loan 1
    const amount1Validation = calculatorUtils.validateNumericInput(
      loanAmount1Value,
      1000,
      Number.MAX_SAFE_INTEGER,
      "Please enter a valid loan amount for Loan 1 (minimum ₹1,000)",
    );

    if (!amount1Validation.valid) {
      calculatorUtils.showError(loanAmount1, amount1Validation.message);
      return;
    }

    const rate1Validation = calculatorUtils.validateNumericInput(
      interestRate1Value,
      1,
      50,
      "Please enter a valid interest rate for Loan 1 between 1% and 50%",
    );

    if (!rate1Validation.valid) {
      calculatorUtils.showError(interestRate1, rate1Validation.message);
      return;
    }

    const tenure1Validation = calculatorUtils.validateNumericInput(
      loanTenure1Value,
      1,
      50,
      "Please enter a valid loan tenure for Loan 1 between 1 and 50 years",
    );

    if (!tenure1Validation.valid) {
      calculatorUtils.showError(loanTenure1, tenure1Validation.message);
      return;
    }

    const fee1Validation = calculatorUtils.validateNumericInput(
      processingFee1Value,
      0,
      loanAmount1Value * 0.1, // Assuming max processing fee is 10% of loan amount
      "Processing fee for Loan 1 cannot exceed 10% of loan amount",
    );

    if (!fee1Validation.valid) {
      calculatorUtils.showError(processingFee1, fee1Validation.message);
      return;
    }

    // Validate inputs for Loan 2
    const amount2Validation = calculatorUtils.validateNumericInput(
      loanAmount2Value,
      1000,
      Number.MAX_SAFE_INTEGER,
      "Please enter a valid loan amount for Loan 2 (minimum ₹1,000)",
    );

    if (!amount2Validation.valid) {
      calculatorUtils.showError(loanAmount2, amount2Validation.message);
      return;
    }

    const rate2Validation = calculatorUtils.validateNumericInput(
      interestRate2Value,
      1,
      50,
      "Please enter a valid interest rate for Loan 2 between 1% and 50%",
    );

    if (!rate2Validation.valid) {
      calculatorUtils.showError(interestRate2, rate2Validation.message);
      return;
    }

    const tenure2Validation = calculatorUtils.validateNumericInput(
      loanTenure2Value,
      1,
      50,
      "Please enter a valid loan tenure for Loan 2 between 1 and 50 years",
    );

    if (!tenure2Validation.valid) {
      calculatorUtils.showError(loanTenure2, tenure2Validation.message);
      return;
    }

    const fee2Validation = calculatorUtils.validateNumericInput(
      processingFee2Value,
      0,
      loanAmount2Value * 0.1, // Assuming max processing fee is 10% of loan amount
      "Processing fee for Loan 2 cannot exceed 10% of loan amount",
    );

    if (!fee2Validation.valid) {
      calculatorUtils.showError(processingFee2, fee2Validation.message);
      return;
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("loan-comparison", {
      loanAmount1: loanAmount1Value,
      interestRate1: interestRate1Value,
      loanTenure1: loanTenure1Value,
      processingFee1: processingFee1Value,

      loanAmount2: loanAmount2Value,
      interestRate2: interestRate2Value,
      loanTenure2: loanTenure2Value,
      processingFee2: processingFee2Value,
    });

    // Calculate EMI for Loan 1
    const tenureInMonths1 = loanTenure1Value * 12;
    const monthlyRate1 = interestRate1Value / (12 * 100);
    const emi1 =
      (loanAmount1Value *
        monthlyRate1 *
        Math.pow(1 + monthlyRate1, tenureInMonths1)) /
      (Math.pow(1 + monthlyRate1, tenureInMonths1) - 1);

    // Calculate EMI for Loan 2
    const tenureInMonths2 = loanTenure2Value * 12;
    const monthlyRate2 = interestRate2Value / (12 * 100);
    const emi2 =
      (loanAmount2Value *
        monthlyRate2 *
        Math.pow(1 + monthlyRate2, tenureInMonths2)) /
      (Math.pow(1 + monthlyRate2, tenureInMonths2) - 1);

    // Calculate total payment and total interest for Loan 1
    const totalPayment1 = emi1 * tenureInMonths1 + processingFee1Value;
    const totalInterest1 = emi1 * tenureInMonths1 - loanAmount1Value;

    // Calculate total payment and total interest for Loan 2
    const totalPayment2 = emi2 * tenureInMonths2 + processingFee2Value;
    const totalInterest2 = emi2 * tenureInMonths2 - loanAmount2Value;

    // Calculate cost difference
    const costDifference = Math.abs(totalPayment1 - totalPayment2);

    // Determine which loan is better (lower total cost)
    const betterLoan = totalPayment1 < totalPayment2 ? 1 : 2;

    // Round values to 2 decimal places
    const roundedEMI1 = calculatorUtils.round(emi1, 2);
    const roundedTotalPayment1 = calculatorUtils.round(totalPayment1, 2);
    const roundedTotalInterest1 = calculatorUtils.round(totalInterest1, 2);

    const roundedEMI2 = calculatorUtils.round(emi2, 2);
    const roundedTotalPayment2 = calculatorUtils.round(totalPayment2, 2);
    const roundedTotalInterest2 = calculatorUtils.round(totalInterest2, 2);

    const roundedCostDifference = calculatorUtils.round(costDifference, 2);

    // Display results
    document.getElementById("loan1-emi").textContent =
      calculatorUtils.formatCurrency(roundedEMI1);
    document.getElementById("loan1-total-interest").textContent =
      calculatorUtils.formatCurrency(roundedTotalInterest1);
    document.getElementById("loan1-total-payment").textContent =
      calculatorUtils.formatCurrency(roundedTotalPayment1);

    document.getElementById("loan2-emi").textContent =
      calculatorUtils.formatCurrency(roundedEMI2);
    document.getElementById("loan2-total-interest").textContent =
      calculatorUtils.formatCurrency(roundedTotalInterest2);
    document.getElementById("loan2-total-payment").textContent =
      calculatorUtils.formatCurrency(roundedTotalPayment2);

    document.getElementById("cost-difference").textContent =
      calculatorUtils.formatCurrency(roundedCostDifference);
    document.getElementById("better-loan").textContent = `Loan ${betterLoan}`;

    results.style.display = "block";

    // Generate and display comparison chart
    generateComparisonChart(
      loanAmount1Value,
      roundedTotalInterest1,
      processingFee1Value,
      loanAmount2Value,
      roundedTotalInterest2,
      processingFee2Value,
    );

    // Save calculation to history
    storageManager.saveCalculationHistory("loan-comparison", {
      loanAmount1: loanAmount1Value,
      interestRate1: interestRate1Value,
      loanTenure1: loanTenure1Value,
      processingFee1: processingFee1Value,
      emi1: roundedEMI1,
      totalInterest1: roundedTotalInterest1,
      totalPayment1: roundedTotalPayment1,

      loanAmount2: loanAmount2Value,
      interestRate2: interestRate2Value,
      loanTenure2: loanTenure2Value,
      processingFee2: processingFee2Value,
      emi2: roundedEMI2,
      totalInterest2: roundedTotalInterest2,
      totalPayment2: roundedTotalPayment2,

      costDifference: roundedCostDifference,
      betterLoan: betterLoan,
    });

    // Show in-article ad after calculation
    const inArticleAd = document.getElementById(
      "ad-between-calculator-results",
    );
    if (inArticleAd) {
      inArticleAd.style.display = "block";
    }
  });
}

/**
 * Generate comparison chart for Loan Comparison Calculator
 */
function generateComparisonChart(
  principal1,
  interest1,
  fee1,
  principal2,
  interest2,
  fee2,
) {
  const chartContainer = document.getElementById("comparison-chart-container");
  if (!chartContainer) return;

  chartContainer.innerHTML = ""; // Clear previous chart

  // Create chart container
  const chartEl = document.createElement("div");
  chartEl.className = "comparison-chart";

  // Create bars for Loan 1
  const loan1Bar = document.createElement("div");
  loan1Bar.className = "loan-bar";
  loan1Bar.innerHTML = `
    <div class="bar-label">Loan 1</div>
    <div class="bar-container">
      <div class="bar-segment principal-segment" style="height: ${
        (principal1 / (principal1 + interest1 + fee1)) * 100
      }%"></div>
      <div class="bar-segment interest-segment" style="height: ${
        (interest1 / (principal1 + interest1 + fee1)) * 100
      }%"></div>
      <div class="bar-segment fee-segment" style="height: ${
        (fee1 / (principal1 + interest1 + fee1)) * 100
      }%"></div>
    </div>
    <div class="bar-value">${calculatorUtils.formatCurrency(
      principal1 + interest1 + fee1,
    )}</div>
  `;

  // Create bars for Loan 2
  const loan2Bar = document.createElement("div");
  loan2Bar.className = "loan-bar";
  loan2Bar.innerHTML = `
    <div class="bar-label">Loan 2</div>
    <div class="bar-container">
      <div class="bar-segment principal-segment" style="height: ${
        (principal2 / (principal2 + interest2 + fee2)) * 100
      }%"></div>
      <div class="bar-segment interest-segment" style="height: ${
        (interest2 / (principal2 + interest2 + fee2)) * 100
      }%"></div>
      <div class="bar-segment fee-segment" style="height: ${
        (fee2 / (principal2 + interest2 + fee2)) * 100
      }%"></div>
    </div>
    <div class="bar-value">${calculatorUtils.formatCurrency(
      principal2 + interest2 + fee2,
    )}</div>
  `;

  // Create legend
  const legendEl = document.createElement("div");
  legendEl.className = "chart-legend";
  legendEl.innerHTML = `
    <div class="legend-item"><span class="legend-color principal-color"></span> Principal</div>
    <div class="legend-item"><span class="legend-color interest-color"></span> Interest</div>
    <div class="legend-item"><span class="legend-color fee-color"></span> Processing Fee</div>
  `;

  // Assemble chart
  chartEl.appendChild(loan1Bar);
  chartEl.appendChild(loan2Bar);

  chartContainer.appendChild(chartEl);
  chartContainer.appendChild(legendEl);
}

/**
 * Initialize Mortgage Calculator
 */
function initMortgageCalculator() {
  // Get form elements
  const form = document.getElementById("mortgage-calculator-form");
  const propertyValue = document.getElementById("property-value");
  const downPaymentType = document.getElementById("down-payment-type");
  const downPaymentAmount = document.getElementById("down-payment-amount");
  const downPaymentPercentage = document.getElementById(
    "down-payment-percentage",
  );
  const interestRate = document.getElementById("interest-rate");
  const loanTerm = document.getElementById("loan-term");
  const includeExtras = document.getElementById("include-extras");
  const propertyTax = document.getElementById("property-tax");
  const homeInsurance = document.getElementById("home-insurance");
  const pmi = document.getElementById("pmi");
  const results = document.getElementById("mortgage-results");
  const extrasContainer = document.getElementById("extras-container");
  const extrasResults = document.getElementById("extras-results");

  // Handle down payment type change
  downPaymentType.addEventListener("change", function () {
    if (this.value === "amount") {
      document.getElementById("down-payment-amount-group").style.display =
        "block";
      document.getElementById("down-payment-percentage-group").style.display =
        "none";
      downPaymentAmount.required = true;
      downPaymentPercentage.required = false;
    } else {
      document.getElementById("down-payment-amount-group").style.display =
        "none";
      document.getElementById("down-payment-percentage-group").style.display =
        "block";
      downPaymentAmount.required = false;
      downPaymentPercentage.required = true;
    }
  });

  // Handle include extras change
  includeExtras.addEventListener("change", function () {
    if (this.value === "yes") {
      extrasContainer.style.display = "block";
    } else {
      extrasContainer.style.display = "none";
    }
  });

  // Calculate down payment amount when percentage changes
  downPaymentPercentage.addEventListener("input", function () {
    const propertyValueNum = parseFloat(propertyValue.value) || 0;
    const percentageValue = parseFloat(this.value) || 0;
    const calculatedAmount = (propertyValueNum * percentageValue) / 100;
    downPaymentAmount.value = calculatedAmount.toFixed(0);
  });

  // Calculate down payment percentage when amount changes
  downPaymentAmount.addEventListener("input", function () {
    const propertyValueNum = parseFloat(propertyValue.value) || 0;
    if (propertyValueNum > 0) {
      const amountValue = parseFloat(this.value) || 0;
      const calculatedPercentage = (amountValue / propertyValueNum) * 100;
      downPaymentPercentage.value = calculatedPercentage.toFixed(1);
    }
  });

  // Update down payment amount when property value changes
  propertyValue.addEventListener("input", function () {
    if (downPaymentType.value === "percentage") {
      const propertyValueNum = parseFloat(this.value) || 0;
      const percentageValue = parseFloat(downPaymentPercentage.value) || 0;
      const calculatedAmount = (propertyValueNum * percentageValue) / 100;
      downPaymentAmount.value = calculatedAmount.toFixed(0);
    }
  });

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("mortgage");
  if (savedValues) {
    propertyValue.value = savedValues.propertyValue;
    downPaymentType.value = savedValues.downPaymentType;
    downPaymentAmount.value = savedValues.downPaymentAmount;
    downPaymentPercentage.value = savedValues.downPaymentPercentage;
    interestRate.value = savedValues.interestRate;
    loanTerm.value = savedValues.loanTerm;
    includeExtras.value = savedValues.includeExtras;

    if (savedValues.propertyTax) propertyTax.value = savedValues.propertyTax;
    if (savedValues.homeInsurance)
      homeInsurance.value = savedValues.homeInsurance;
    if (savedValues.pmi) pmi.value = savedValues.pmi;

    // Trigger change events to show/hide appropriate fields
    const downPaymentTypeEvent = new Event("change");
    downPaymentType.dispatchEvent(downPaymentTypeEvent);

    const includeExtrasEvent = new Event("change");
    includeExtras.dispatchEvent(includeExtrasEvent);
  } else {
    // Set default values
    propertyValue.value = "300000";
    downPaymentAmount.value = "60000";
    downPaymentPercentage.value = "20";
    interestRate.value = "4.5";
    loanTerm.value = "30";
    propertyTax.value = "3000";
    homeInsurance.value = "1200";
    pmi.value = "0.5";
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const propertyValueNum = parseFloat(propertyValue.value);
    const downPaymentTypeValue = downPaymentType.value;
    const downPaymentAmountNum = parseFloat(downPaymentAmount.value) || 0;
    const downPaymentPercentageNum =
      parseFloat(downPaymentPercentage.value) || 0;
    const interestRateNum = parseFloat(interestRate.value);
    const loanTermNum = parseInt(loanTerm.value);
    const includeExtrasValue = includeExtras.value;
    const propertyTaxNum = parseFloat(propertyTax.value) || 0;
    const homeInsuranceNum = parseFloat(homeInsurance.value) || 0;
    const pmiNum = parseFloat(pmi.value) || 0;

    // Validate property value
    const propertyValueValidation = calculatorUtils.validateNumericInput(
      propertyValueNum,
      10000,
      Number.MAX_SAFE_INTEGER,
      "Please enter a valid property value (minimum $10,000)",
    );

    if (!propertyValueValidation.valid) {
      calculatorUtils.showError(propertyValue, propertyValueValidation.message);
      return;
    }

    // Validate down payment
    let downPaymentValidation;
    if (downPaymentTypeValue === "amount") {
      downPaymentValidation = calculatorUtils.validateNumericInput(
        downPaymentAmountNum,
        0,
        propertyValueNum,
        "Down payment cannot exceed property value",
      );

      if (!downPaymentValidation.valid) {
        calculatorUtils.showError(
          downPaymentAmount,
          downPaymentValidation.message,
        );
        return;
      }
    } else {
      downPaymentValidation = calculatorUtils.validateNumericInput(
        downPaymentPercentageNum,
        0,
        100,
        "Please enter a valid percentage between 0 and 100",
      );

      if (!downPaymentValidation.valid) {
        calculatorUtils.showError(
          downPaymentPercentage,
          downPaymentValidation.message,
        );
        return;
      }
    }

    // Validate interest rate
    const interestRateValidation = calculatorUtils.validateNumericInput(
      interestRateNum,
      0.1,
      50,
      "Please enter a valid interest rate between 0.1% and 50%",
    );

    if (!interestRateValidation.valid) {
      calculatorUtils.showError(interestRate, interestRateValidation.message);
      return;
    }

    // Validate loan term
    const loanTermValidation = calculatorUtils.validateNumericInput(
      loanTermNum,
      1,
      50,
      "Please enter a valid loan term between 1 and 50 years",
    );

    if (!loanTermValidation.valid) {
      calculatorUtils.showError(loanTerm, loanTermValidation.message);
      return;
    }

    // If including extras, validate those inputs
    if (includeExtrasValue === "yes") {
      const propertyTaxValidation = calculatorUtils.validateNumericInput(
        propertyTaxNum,
        0,
        propertyValueNum * 0.1, // Assuming max property tax is 10% of property value
        "Property tax seems too high",
      );

      if (!propertyTaxValidation.valid) {
        calculatorUtils.showError(propertyTax, propertyTaxValidation.message);
        return;
      }

      const homeInsuranceValidation = calculatorUtils.validateNumericInput(
        homeInsuranceNum,
        0,
        propertyValueNum * 0.05, // Assuming max insurance is 5% of property value
        "Home insurance seems too high",
      );

      if (!homeInsuranceValidation.valid) {
        calculatorUtils.showError(
          homeInsurance,
          homeInsuranceValidation.message,
        );
        return;
      }

      const pmiValidation = calculatorUtils.validateNumericInput(
        pmiNum,
        0,
        5,
        "Please enter a valid PMI rate between 0% and 5%",
      );

      if (!pmiValidation.valid) {
        calculatorUtils.showError(pmi, pmiValidation.message);
        return;
      }
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("mortgage", {
      propertyValue: propertyValueNum,
      downPaymentType: downPaymentTypeValue,
      downPaymentAmount: downPaymentAmountNum,
      downPaymentPercentage: downPaymentPercentageNum,
      interestRate: interestRateNum,
      loanTerm: loanTermNum,
      includeExtras: includeExtrasValue,
      propertyTax: propertyTaxNum,
      homeInsurance: homeInsuranceNum,
      pmi: pmiNum,
    });

    // Calculate loan amount
    const loanAmount = propertyValueNum - downPaymentAmountNum;

    // Calculate monthly interest rate (annual rate / 12 / 100)
    const monthlyRate = interestRateNum / (12 * 100);

    // Calculate loan term in months
    const loanTermMonths = loanTermNum * 12;

    // Calculate monthly principal and interest payment using formula:
    // M = P [ i(1 + i)^n ] / [ (1 + i)^n - 1]
    // Where M is monthly payment, P is principal, i is monthly interest rate, n is number of months
    const monthlyPrincipalInterest =
      (loanAmount * monthlyRate * Math.pow(1 + monthlyRate, loanTermMonths)) /
      (Math.pow(1 + monthlyRate, loanTermMonths) - 1);

    // Calculate total payment over the life of the loan
    const totalPayment = monthlyPrincipalInterest * loanTermMonths;

    // Calculate total interest
    const totalInterest = totalPayment - loanAmount;

    // Calculate additional monthly costs if included
    let monthlyPropertyTax = 0;
    let monthlyHomeInsurance = 0;
    let monthlyPMI = 0;
    let totalMonthlyPayment = monthlyPrincipalInterest;

    if (includeExtrasValue === "yes") {
      // Monthly property tax
      monthlyPropertyTax = propertyTaxNum / 12;

      // Monthly home insurance
      monthlyHomeInsurance = homeInsuranceNum / 12;

      // Calculate PMI if down payment is less than 20%
      if (downPaymentPercentageNum < 20) {
        monthlyPMI = (loanAmount * (pmiNum / 100)) / 12;
      }

      // Add to total monthly payment
      totalMonthlyPayment +=
        monthlyPropertyTax + monthlyHomeInsurance + monthlyPMI;

      // Show extras results
      extrasResults.style.display = "block";
    } else {
      extrasResults.style.display = "none";
    }

    // Round values
    const roundedMonthlyPrincipalInterest = calculatorUtils.round(
      monthlyPrincipalInterest,
      2,
    );
    const roundedTotalMonthlyPayment = calculatorUtils.round(
      totalMonthlyPayment,
      2,
    );
    const roundedLoanAmount = calculatorUtils.round(loanAmount, 2);
    const roundedDownPayment = calculatorUtils.round(downPaymentAmountNum, 2);
    const roundedTotalInterest = calculatorUtils.round(totalInterest, 2);
    const roundedTotalPayment = calculatorUtils.round(totalPayment, 2);
    const roundedMonthlyPropertyTax = calculatorUtils.round(
      monthlyPropertyTax,
      2,
    );
    const roundedMonthlyHomeInsurance = calculatorUtils.round(
      monthlyHomeInsurance,
      2,
    );
    const roundedMonthlyPMI = calculatorUtils.round(monthlyPMI, 2);

    // Display results
    document.getElementById("monthly-payment").textContent =
      calculatorUtils.formatCurrency(roundedTotalMonthlyPayment);
    document.getElementById("loan-amount").textContent =
      calculatorUtils.formatCurrency(roundedLoanAmount);
    document.getElementById("down-payment").textContent =
      calculatorUtils.formatCurrency(roundedDownPayment);
    document.getElementById("total-interest").textContent =
      calculatorUtils.formatCurrency(roundedTotalInterest);
    document.getElementById("total-payment").textContent =
      calculatorUtils.formatCurrency(roundedTotalPayment);

    if (includeExtrasValue === "yes") {
      document.getElementById("principal-interest").textContent =
        calculatorUtils.formatCurrency(roundedMonthlyPrincipalInterest);
      document.getElementById("monthly-property-tax").textContent =
        calculatorUtils.formatCurrency(roundedMonthlyPropertyTax);
      document.getElementById("monthly-insurance").textContent =
        calculatorUtils.formatCurrency(roundedMonthlyHomeInsurance);
      document.getElementById("monthly-pmi").textContent =
        calculatorUtils.formatCurrency(roundedMonthlyPMI);
    }

    // Generate and display chart
    generateMortgageChart(
      loanAmount,
      roundedTotalInterest,
      includeExtrasValue === "yes",
      propertyTaxNum,
      homeInsuranceNum,
      monthlyPMI * 12 * loanTermNum,
    );

    // Show results
    results.style.display = "block";

    // Save calculation to history
    storageManager.saveCalculationHistory("mortgage", {
      propertyValue: propertyValueNum,
      downPayment: downPaymentAmountNum,
      loanAmount: roundedLoanAmount,
      interestRate: interestRateNum,
      loanTerm: loanTermNum,
      monthlyPayment: roundedTotalMonthlyPayment,
      totalInterest: roundedTotalInterest,
    });

    // Show in-article ad after calculation
    const inArticleAd = document.getElementById(
      "ad-between-calculator-results",
    );
    if (inArticleAd) {
      inArticleAd.style.display = "block";
    }
  });
}

/**
 * Generate Mortgage breakdown chart
 */
function generateMortgageChart(
  principal,
  totalInterest,
  includeExtras,
  propertyTax,
  homeInsurance,
  totalPMI,
) {
  const chartContainer = document.getElementById("mortgage-chart-container");
  if (!chartContainer) return;

  chartContainer.innerHTML = ""; // Clear previous chart

  // Create a simple pie chart using HTML/CSS
  const chartEl = document.createElement("div");
  chartEl.className = "pie-chart";

  let totalAmount = principal + totalInterest;
  let segments = [
    { name: "Principal", value: principal, color: "var(--primary-color)" },
    { name: "Interest", value: totalInterest, color: "var(--secondary-color)" },
  ];

  if (includeExtras) {
    const totalPropertyTax =
      (propertyTax * (principal + totalInterest)) / (principal * 12);
    const totalHomeInsurance =
      (homeInsurance * (principal + totalInterest)) / (principal * 12);

    totalAmount += totalPropertyTax + totalHomeInsurance + totalPMI;

    segments.push(
      {
        name: "Property Tax",
        value: totalPropertyTax,
        color: "var(--accent-color-1)",
      },
      {
        name: "Home Insurance",
        value: totalHomeInsurance,
        color: "var(--accent-color-2)",
      },
    );

    if (totalPMI > 0) {
      segments.push({
        name: "PMI",
        value: totalPMI,
        color: "var(--accent-color-3)",
      });
    }
  }

  // Create chart legend
  const legendEl = document.createElement("div");
  legendEl.className = "chart-legend";

  // Calculate percentages and create gradient for pie chart
  let gradientString = "";
  let startPercentage = 0;

  segments.forEach((segment) => {
    const percentage = (segment.value / totalAmount) * 100;
    const endPercentage = startPercentage + percentage;

    gradientString += `${segment.color} ${startPercentage}% ${endPercentage}%, `;

    const legendItem = document.createElement("div");
    legendItem.className = "legend-item";
    legendItem.innerHTML = `
      <span class="legend-color" style="background-color: ${
        segment.color
      };"></span>
      <span class="legend-text">${
        segment.name
      }: ${calculatorUtils.formatCurrency(segment.value)} (${percentage.toFixed(
      1,
    )}%)</span>
    `;

    legendEl.appendChild(legendItem);
    startPercentage = endPercentage;
  });

  // Remove trailing comma and space
  gradientString = gradientString.slice(0, -2);

  // Create the actual pie chart
  const pieEl = document.createElement("div");
  pieEl.className = "pie";
  pieEl.style.background = `conic-gradient(${gradientString})`;

  chartEl.appendChild(pieEl);
  chartContainer.appendChild(chartEl);
  chartContainer.appendChild(legendEl);
}

/**
 * Initialize Amortization Schedule Calculator
 */
function initAmortizationCalculator() {
  // Get form elements
  const form = document.getElementById("amortization-calculator-form");
  const loanAmount = document.getElementById("loan-amount");
  const interestRate = document.getElementById("interest-rate");
  const loanTenure = document.getElementById("loan-tenure");
  const tenureType = document.getElementById("tenure-type");
  const results = document.getElementById("amortization-results");
  const scheduleTable = document.getElementById("amortization-schedule");

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("amortization");
  if (savedValues) {
    loanAmount.value = savedValues.loanAmount;
    interestRate.value = savedValues.interestRate;
    loanTenure.value = savedValues.loanTenure;
    tenureType.value = savedValues.tenureType;
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const loanAmountValue = parseFloat(loanAmount.value);
    const interestRateValue = parseFloat(interestRate.value);
    const loanTenureValue = parseInt(loanTenure.value);
    const tenureTypeValue = tenureType.value;

    // Validate inputs
    const amountValidation = calculatorUtils.validateNumericInput(
      loanAmountValue,
      1000,
      Number.MAX_SAFE_INTEGER,
      "Please enter a valid loan amount (minimum ₹1,000)",
    );

    if (!amountValidation.valid) {
      calculatorUtils.showError(loanAmount, amountValidation.message);
      return;
    }

    const rateValidation = calculatorUtils.validateNumericInput(
      interestRateValue,
      1,
      50,
      "Please enter a valid interest rate between 1% and 50%",
    );

    if (!rateValidation.valid) {
      calculatorUtils.showError(interestRate, rateValidation.message);
      return;
    }

    const tenureValidation = calculatorUtils.validateNumericInput(
      loanTenureValue,
      1,
      tenureTypeValue === "year" ? 50 : 600,
      `Please enter a valid loan tenure between 1 and ${
        tenureTypeValue === "year" ? "50 years" : "600 months"
      }`,
    );

    if (!tenureValidation.valid) {
      calculatorUtils.showError(loanTenure, tenureValidation.message);
      return;
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("amortization", {
      loanAmount: loanAmountValue,
      interestRate: interestRateValue,
      loanTenure: loanTenureValue,
      tenureType: tenureTypeValue,
    });

    // Calculate EMI
    // Convert tenure to months if in years
    const tenureInMonths =
      tenureTypeValue === "year" ? loanTenureValue * 12 : loanTenureValue;

    // Calculate monthly interest rate (annual rate / 12 / 100)
    const monthlyRate = interestRateValue / (12 * 100);

    // Calculate EMI using formula: EMI = [P × r × (1 + r)^n] ÷ [(1 + r)^n - 1]
    // Where P is principal, r is monthly rate, n is number of months
    const emi =
      (loanAmountValue *
        monthlyRate *
        Math.pow(1 + monthlyRate, tenureInMonths)) /
      (Math.pow(1 + monthlyRate, tenureInMonths) - 1);

    // Calculate total payment and total interest
    const totalPayment = emi * tenureInMonths;
    const totalInterest = totalPayment - loanAmountValue;

    // Round values to 2 decimal places
    const roundedEMI = calculatorUtils.round(emi, 2);
    const roundedTotalPayment = calculatorUtils.round(totalPayment, 2);
    const roundedTotalInterest = calculatorUtils.round(totalInterest, 2);

    // Display summary results
    document.getElementById("monthly-emi").textContent =
      calculatorUtils.formatCurrency(roundedEMI);
    document.getElementById("total-interest").textContent =
      calculatorUtils.formatCurrency(roundedTotalInterest);
    document.getElementById("total-payment").textContent =
      calculatorUtils.formatCurrency(roundedTotalPayment);

    // Generate amortization schedule
    generateAmortizationSchedule(
      loanAmountValue,
      monthlyRate,
      tenureInMonths,
      roundedEMI,
      scheduleTable,
    );

    results.style.display = "block";

    // Save calculation to history
    storageManager.saveCalculationHistory("amortization", {
      loanAmount: loanAmountValue,
      interestRate: interestRateValue,
      loanTenure: loanTenureValue,
      tenureType: tenureTypeValue,
      emi: roundedEMI,
      totalInterest: roundedTotalInterest,
      totalPayment: roundedTotalPayment,
    });
  });
}

/**
 * Generate amortization schedule table
 */
function generateAmortizationSchedule(
  principal,
  monthlyRate,
  tenureInMonths,
  emi,
  tableElement,
) {
  // Clear previous table content
  tableElement.innerHTML = "";

  // Create table header
  const tableHeader = document.createElement("thead");
  tableHeader.innerHTML = `
    <tr>
      <th>Month</th>
      <th>Principal (₹)</th>
      <th>Interest (₹)</th>
      <th>Balance (₹)</th>
    </tr>
  `;
  tableElement.appendChild(tableHeader);

  // Create table body
  const tableBody = document.createElement("tbody");

  let remainingBalance = principal;
  let totalPrincipal = 0;
  let totalInterest = 0;

  // Generate rows for each month
  // Limit to first 12 months, last 12 months, and every 12th month in between
  // to avoid generating too many rows for long-term loans
  for (let month = 1; month <= tenureInMonths; month++) {
    // Calculate interest for this month
    const interestPayment = remainingBalance * monthlyRate;

    // Calculate principal for this month
    const principalPayment = emi - interestPayment;

    // Update remaining balance
    remainingBalance -= principalPayment;
    if (remainingBalance < 0) remainingBalance = 0;

    // Update totals
    totalPrincipal += principalPayment;
    totalInterest += interestPayment;

    // Determine if this row should be displayed
    // Show first 12 months, last 12 months, and every 12th month in between
    const isFirstYear = month <= 12;
    const isLastYear = month > tenureInMonths - 12;
    const isYearEnd = month % 12 === 0;

    if (isFirstYear || isLastYear || isYearEnd) {
      const row = document.createElement("tr");

      // Add month number
      const monthCell = document.createElement("td");
      monthCell.textContent = month;
      row.appendChild(monthCell);

      // Add principal payment
      const principalCell = document.createElement("td");
      principalCell.textContent =
        calculatorUtils.formatCurrency(principalPayment);
      row.appendChild(principalCell);

      // Add interest payment
      const interestCell = document.createElement("td");
      interestCell.textContent =
        calculatorUtils.formatCurrency(interestPayment);
      row.appendChild(interestCell);

      // Add remaining balance
      const balanceCell = document.createElement("td");
      balanceCell.textContent =
        calculatorUtils.formatCurrency(remainingBalance);
      row.appendChild(balanceCell);

      tableBody.appendChild(row);
    }
  }

  tableElement.appendChild(tableBody);
}

/**
 * Initialize Prepayment Calculator
 */
function initPrepaymentCalculator() {
  // Get form elements
  const form = document.getElementById("prepayment-calculator-form");
  const loanAmount = document.getElementById("loan-amount");
  const interestRate = document.getElementById("interest-rate");
  const loanTenure = document.getElementById("loan-tenure");
  const prepaymentAmount = document.getElementById("prepayment-amount");
  const prepaymentFrequency = document.getElementById("prepayment-frequency");
  const prepaymentStartMonth = document.getElementById(
    "prepayment-start-month",
  );
  const prepaymentStrategy = document.getElementById("prepayment-strategy");
  const prepaymentPenalty = document.getElementById("prepayment-penalty");
  const results = document.getElementById("prepayment-results");

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("prepayment");
  if (savedValues) {
    loanAmount.value = savedValues.loanAmount;
    interestRate.value = savedValues.interestRate;
    loanTenure.value = savedValues.loanTenure;
    prepaymentAmount.value = savedValues.prepaymentAmount;
    prepaymentFrequency.value = savedValues.prepaymentFrequency;
    prepaymentStartMonth.value = savedValues.prepaymentStartMonth;
    prepaymentStrategy.value = savedValues.prepaymentStrategy;
    prepaymentPenalty.value = savedValues.prepaymentPenalty;
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const loanAmountValue = parseFloat(loanAmount.value);
    const interestRateValue = parseFloat(interestRate.value);
    const loanTenureValue = parseInt(loanTenure.value);
    const prepaymentAmountValue = parseFloat(prepaymentAmount.value) || 0;
    const prepaymentFrequencyValue = prepaymentFrequency.value;
    const prepaymentStartMonthValue = parseInt(prepaymentStartMonth.value);
    const prepaymentStrategyValue = prepaymentStrategy.value;
    const prepaymentPenaltyValue = parseFloat(prepaymentPenalty.value) || 0;

    // Validate inputs
    const amountValidation = calculatorUtils.validateNumericInput(
      loanAmountValue,
      100000,
      Number.MAX_SAFE_INTEGER,
      "Please enter a valid loan amount (minimum ₹1,00,000)",
    );

    if (!amountValidation.valid) {
      calculatorUtils.showError(loanAmount, amountValidation.message);
      return;
    }

    const rateValidation = calculatorUtils.validateNumericInput(
      interestRateValue,
      1,
      50,
      "Please enter a valid interest rate between 1% and 50%",
    );

    if (!rateValidation.valid) {
      calculatorUtils.showError(interestRate, rateValidation.message);
      return;
    }

    const tenureValidation = calculatorUtils.validateNumericInput(
      loanTenureValue,
      1,
      50,
      "Please enter a valid loan tenure between 1 and 50 years",
    );

    if (!tenureValidation.valid) {
      calculatorUtils.showError(loanTenure, tenureValidation.message);
      return;
    }

    if (prepaymentAmountValue > 0) {
      const prepaymentValidation = calculatorUtils.validateNumericInput(
        prepaymentAmountValue,
        1000,
        loanAmountValue,
        "Prepayment amount should be between ₹1,000 and the loan amount",
      );

      if (!prepaymentValidation.valid) {
        calculatorUtils.showError(
          prepaymentAmount,
          prepaymentValidation.message,
        );
        return;
      }
    }

    const startMonthValidation = calculatorUtils.validateNumericInput(
      prepaymentStartMonthValue,
      1,
      loanTenureValue * 12,
      `Start month should be between 1 and ${loanTenureValue * 12}`,
    );

    if (!startMonthValidation.valid) {
      calculatorUtils.showError(
        prepaymentStartMonth,
        startMonthValidation.message,
      );
      return;
    }

    const penaltyValidation = calculatorUtils.validateNumericInput(
      prepaymentPenaltyValue,
      0,
      5,
      "Prepayment penalty should be between 0% and 5%",
    );

    if (!penaltyValidation.valid) {
      calculatorUtils.showError(prepaymentPenalty, penaltyValidation.message);
      return;
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("prepayment", {
      loanAmount: loanAmountValue,
      interestRate: interestRateValue,
      loanTenure: loanTenureValue,
      prepaymentAmount: prepaymentAmountValue,
      prepaymentFrequency: prepaymentFrequencyValue,
      prepaymentStartMonth: prepaymentStartMonthValue,
      prepaymentStrategy: prepaymentStrategyValue,
      prepaymentPenalty: prepaymentPenaltyValue,
    });

    // Calculate original loan details
    const tenureInMonths = loanTenureValue * 12;
    const monthlyRate = interestRateValue / (12 * 100);

    // Calculate original EMI
    const originalEmi =
      (loanAmountValue *
        monthlyRate *
        Math.pow(1 + monthlyRate, tenureInMonths)) /
      (Math.pow(1 + monthlyRate, tenureInMonths) - 1);

    // Calculate original total payment and interest
    const originalTotalPayment = originalEmi * tenureInMonths;
    const originalTotalInterest = originalTotalPayment - loanAmountValue;

    // Calculate prepayment impact
    let newTenureInMonths = tenureInMonths;
    let newEmi = originalEmi;
    let newTotalInterest = originalTotalInterest;
    let totalPrepaymentAmount = 0;
    let penaltyAmount = 0;

    // Create amortization schedule
    const schedule = [];
    let remainingPrincipal = loanAmountValue;
    let month = 1;

    // Calculate prepayment frequency in months
    let prepaymentIntervalMonths = 0;
    switch (prepaymentFrequencyValue) {
      case "one-time":
        prepaymentIntervalMonths = 0; // One-time prepayment
        break;
      case "monthly":
        prepaymentIntervalMonths = 1;
        break;
      case "quarterly":
        prepaymentIntervalMonths = 3;
        break;
      case "half-yearly":
        prepaymentIntervalMonths = 6;
        break;
      case "yearly":
        prepaymentIntervalMonths = 12;
        break;
    }

    // Generate amortization schedule with prepayments
    while (remainingPrincipal > 0 && month <= tenureInMonths) {
      const interestPayment = remainingPrincipal * monthlyRate;
      const principalPayment = originalEmi - interestPayment;

      // Apply regular EMI payment
      remainingPrincipal -= principalPayment;

      // Check if prepayment should be applied this month
      let prepaymentApplied = 0;
      if (prepaymentAmountValue > 0 && month >= prepaymentStartMonthValue) {
        if (
          prepaymentFrequencyValue === "one-time" &&
          month === prepaymentStartMonthValue
        ) {
          // One-time prepayment
          prepaymentApplied = Math.min(
            prepaymentAmountValue,
            remainingPrincipal,
          );
        } else if (
          prepaymentIntervalMonths > 0 &&
          (month - prepaymentStartMonthValue) % prepaymentIntervalMonths === 0
        ) {
          // Recurring prepayment
          prepaymentApplied = Math.min(
            prepaymentAmountValue,
            remainingPrincipal,
          );
        }

        // Apply prepayment
        if (prepaymentApplied > 0) {
          // Calculate penalty if applicable
          const currentPenalty =
            prepaymentApplied * (prepaymentPenaltyValue / 100);
          penaltyAmount += currentPenalty;

          // Reduce principal by prepayment amount
          remainingPrincipal -= prepaymentApplied;
          totalPrepaymentAmount += prepaymentApplied;

          // Recalculate EMI if strategy is to reduce EMI
          if (
            prepaymentStrategyValue === "reduce-emi" &&
            remainingPrincipal > 0
          ) {
            const remainingMonths = tenureInMonths - month;
            newEmi =
              (remainingPrincipal *
                monthlyRate *
                Math.pow(1 + monthlyRate, remainingMonths)) /
              (Math.pow(1 + monthlyRate, remainingMonths) - 1);
          }
        }
      }

      // Add to schedule
      schedule.push({
        month,
        emi: originalEmi,
        principalPayment,
        interestPayment,
        remainingPrincipal: Math.max(0, remainingPrincipal),
        prepaymentApplied,
      });

      month++;

      // Break if principal is fully paid
      if (remainingPrincipal <= 0) {
        break;
      }
    }

    // Calculate new tenure and total interest
    newTenureInMonths = schedule.length;

    // Calculate new total interest
    newTotalInterest = 0;
    for (const payment of schedule) {
      newTotalInterest += payment.interestPayment;
    }

    // Calculate time saved
    const timeSavedMonths = tenureInMonths - newTenureInMonths;
    const timeSavedYears = Math.floor(timeSavedMonths / 12);
    const remainingMonths = timeSavedMonths % 12;

    // Format results
    const formattedOriginalEmi = calculatorUtils.formatCurrency(
      calculatorUtils.round(originalEmi, 2),
    );
    const formattedOriginalInterest = calculatorUtils.formatCurrency(
      calculatorUtils.round(originalTotalInterest, 2),
    );
    const formattedNewEmi = calculatorUtils.formatCurrency(
      calculatorUtils.round(newEmi, 2),
    );
    const formattedNewInterest = calculatorUtils.formatCurrency(
      calculatorUtils.round(newTotalInterest, 2),
    );
    const formattedInterestSavings = calculatorUtils.formatCurrency(
      calculatorUtils.round(originalTotalInterest - newTotalInterest, 2),
    );
    const formattedTotalPrepayment = calculatorUtils.formatCurrency(
      calculatorUtils.round(totalPrepaymentAmount, 2),
    );
    const formattedPenaltyAmount = calculatorUtils.formatCurrency(
      calculatorUtils.round(penaltyAmount, 2),
    );

    // Display results
    document.getElementById("original-emi").textContent = formattedOriginalEmi;
    document.getElementById("original-interest").textContent =
      formattedOriginalInterest;
    document.getElementById(
      "original-tenure",
    ).textContent = `${loanTenureValue} years (${tenureInMonths} months)`;

    document.getElementById("new-emi").textContent = formattedNewEmi;
    document.getElementById("new-interest").textContent = formattedNewInterest;
    document.getElementById("new-tenure").textContent = `${Math.floor(
      newTenureInMonths / 12,
    )} years (${newTenureInMonths} months)`;

    document.getElementById("interest-savings").textContent =
      formattedInterestSavings;
    document.getElementById(
      "time-saved",
    ).textContent = `${timeSavedYears} years (${timeSavedMonths} months)`;
    document.getElementById("total-prepayment").textContent =
      formattedTotalPrepayment;
    document.getElementById("penalty-amount").textContent =
      formattedPenaltyAmount;

    results.style.display = "block";

    // Generate and display chart
    generatePrepaymentChart(
      loanAmountValue,
      originalTotalInterest,
      newTotalInterest,
      totalPrepaymentAmount,
    );

    // Save calculation to history
    storageManager.saveCalculationHistory("prepayment", {
      loanAmount: loanAmountValue,
      interestRate: interestRateValue,
      loanTenure: loanTenureValue,
      prepaymentAmount: prepaymentAmountValue,
      prepaymentFrequency: prepaymentFrequencyValue,
      prepaymentStartMonth: prepaymentStartMonthValue,
      prepaymentStrategy: prepaymentStrategyValue,
      prepaymentPenalty: prepaymentPenaltyValue,
      originalEmi: calculatorUtils.round(originalEmi, 2),
      originalInterest: calculatorUtils.round(originalTotalInterest, 2),
      newEmi: calculatorUtils.round(newEmi, 2),
      newInterest: calculatorUtils.round(newTotalInterest, 2),
      interestSavings: calculatorUtils.round(
        originalTotalInterest - newTotalInterest,
        2,
      ),
      timeSavedMonths: timeSavedMonths,
    });
  });
}

/**
 * Generate Prepayment comparison chart
 */
function generatePrepaymentChart(
  principal,
  originalInterest,
  newInterest,
  totalPrepayment,
) {
  const chartContainer = document.getElementById("prepayment-chart-container");
  if (!chartContainer) return;

  chartContainer.innerHTML = ""; // Clear previous chart

  // Create chart title
  const chartTitle = document.createElement("h4");
  chartTitle.textContent = "Loan Payment Breakdown Comparison";
  chartTitle.style.textAlign = "center";
  chartTitle.style.marginBottom = "1.5rem";
  chartContainer.appendChild(chartTitle);

  // Create a simple bar chart using HTML/CSS
  const chartEl = document.createElement("div");
  chartEl.className = "simple-chart";

  // Original loan breakdown
  const originalBar1 = document.createElement("div");
  originalBar1.className = "chart-bar";
  originalBar1.style.height = "100%";
  originalBar1.style.backgroundColor = "var(--primary-color)";
  originalBar1.innerHTML = `<span class="bar-label">Principal<br>${calculatorUtils.formatCurrency(
    principal,
  )}</span>`;

  const originalBar2 = document.createElement("div");
  originalBar2.className = "chart-bar";
  const originalInterestHeight = (originalInterest / principal) * 100;
  originalBar2.style.height = `${Math.min(originalInterestHeight, 100)}%`;
  originalBar2.style.backgroundColor = "var(--secondary-color)";
  originalBar2.innerHTML = `<span class="bar-label">Interest<br>${calculatorUtils.formatCurrency(
    originalInterest,
  )}</span>`;

  // New loan breakdown
  const newBar1 = document.createElement("div");
  newBar1.className = "chart-bar";
  newBar1.style.height = "100%";
  newBar1.style.backgroundColor = "var(--primary-color)";
  newBar1.innerHTML = `<span class="bar-label">Principal<br>${calculatorUtils.formatCurrency(
    principal,
  )}</span>`;

  const newBar2 = document.createElement("div");
  newBar2.className = "chart-bar";
  const newInterestHeight = (newInterest / principal) * 100;
  newBar2.style.height = `${Math.min(newInterestHeight, 100)}%`;
  newBar2.style.backgroundColor = "var(--secondary-color)";
  newBar2.innerHTML = `<span class="bar-label">Interest<br>${calculatorUtils.formatCurrency(
    newInterest,
  )}</span>`;

  const newBar3 = document.createElement("div");
  newBar3.className = "chart-bar";
  const prepaymentHeight = (totalPrepayment / principal) * 100;
  newBar3.style.height = `${Math.min(prepaymentHeight, 100)}%`;
  newBar3.style.backgroundColor = "var(--investment-color)";
  newBar3.innerHTML = `<span class="bar-label">Prepayment<br>${calculatorUtils.formatCurrency(
    totalPrepayment,
  )}</span>`;

  // Create chart groups
  const originalGroup = document.createElement("div");
  originalGroup.className = "chart-group";
  originalGroup.style.display = "flex";
  originalGroup.style.flexDirection = "column";
  originalGroup.style.alignItems = "center";
  originalGroup.style.gap = "0.5rem";

  const originalLabel = document.createElement("div");
  originalLabel.textContent = "Without Prepayment";
  originalLabel.style.fontWeight = "bold";
  originalLabel.style.marginBottom = "1rem";

  const originalBars = document.createElement("div");
  originalBars.style.display = "flex";
  originalBars.style.gap = "1rem";
  originalBars.appendChild(originalBar1);
  originalBars.appendChild(originalBar2);

  originalGroup.appendChild(originalLabel);
  originalGroup.appendChild(originalBars);

  const newGroup = document.createElement("div");
  newGroup.className = "chart-group";
  newGroup.style.display = "flex";
  newGroup.style.flexDirection = "column";
  newGroup.style.alignItems = "center";
  newGroup.style.gap = "0.5rem";

  const newLabel = document.createElement("div");
  newLabel.textContent = "With Prepayment";
  newLabel.style.fontWeight = "bold";
  newLabel.style.marginBottom = "1rem";

  const newBars = document.createElement("div");
  newBars.style.display = "flex";
  newBars.style.gap = "1rem";
  newBars.appendChild(newBar1);
  newBars.appendChild(newBar3);
  newBars.appendChild(newBar2);

  newGroup.appendChild(newLabel);
  newGroup.appendChild(newBars);

  // Add groups to chart
  chartEl.appendChild(originalGroup);
  chartEl.appendChild(newGroup);
  chartContainer.appendChild(chartEl);

  // Add savings information
  const savingsInfo = document.createElement("div");
  savingsInfo.className = "savings-info";
  savingsInfo.style.textAlign = "center";
  savingsInfo.style.marginTop = "2rem";
  savingsInfo.style.padding = "1rem";
  savingsInfo.style.backgroundColor = "rgba(56, 176, 0, 0.1)";
  savingsInfo.style.borderRadius = "0.5rem";
  savingsInfo.style.fontWeight = "bold";
  savingsInfo.innerHTML = `Interest Savings: <span style="color: var(--investment-color);">${calculatorUtils.formatCurrency(
    originalInterest - newInterest,
  )}</span>`;
  chartContainer.appendChild(savingsInfo);
}
