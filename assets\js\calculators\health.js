/**
 * Health Calculator Scripts
 */

document.addEventListener("DOMContentLoaded", function () {
  // Initialize BMI Calculator if it exists on the page
  const bmiCalculatorForm = document.getElementById("bmi-calculator-form");
  if (bmiCalculatorForm) {
    initBMICalculator();
  }

  // Initialize Calorie Calculator if it exists on the page
  const calorieCalculatorForm = document.getElementById(
    "calorie-calculator-form",
  );
  if (calorieCalculatorForm) {
    initCalorieCalculator();
  }

  // Initialize Pregnancy Due Date Calculator if it exists on the page
  const pregnancyCalculatorForm = document.getElementById(
    "pregnancy-calculator-form",
  );
  if (pregnancyCalculatorForm) {
    initPregnancyCalculator();
  }

  // Initialize Body Fat Percentage Calculator if it exists on the page
  const bodyFatCalculatorForm = document.getElementById(
    "body-fat-calculator-form",
  );
  if (bodyFatCalculatorForm) {
    initBodyFatCalculator();
  }
});

/**
 * Initialize BMI Calculator
 */
function initBMICalculator() {
  // Get form elements
  const form = document.getElementById("bmi-calculator-form");
  const heightUnit = document.getElementById("height-unit");
  const weightUnit = document.getElementById("weight-unit");
  const heightCm = document.getElementById("height-cm");
  const heightFt = document.getElementById("height-ft");
  const heightIn = document.getElementById("height-in");
  const weightKg = document.getElementById("weight-kg");
  const weightLb = document.getElementById("weight-lb");
  const results = document.getElementById("bmi-results");

  // Handle unit change
  heightUnit.addEventListener("change", function () {
    if (this.value === "cm") {
      document.getElementById("height-cm-group").style.display = "block";
      document.getElementById("height-ft-in-group").style.display = "none";
      heightCm.required = true;
      heightFt.required = false;
      heightIn.required = false;
    } else {
      document.getElementById("height-cm-group").style.display = "none";
      document.getElementById("height-ft-in-group").style.display = "flex";
      heightCm.required = false;
      heightFt.required = true;
      heightIn.required = true;
    }
  });

  weightUnit.addEventListener("change", function () {
    if (this.value === "kg") {
      document.getElementById("weight-kg-group").style.display = "block";
      document.getElementById("weight-lb-group").style.display = "none";
      weightKg.required = true;
      weightLb.required = false;
    } else {
      document.getElementById("weight-kg-group").style.display = "none";
      document.getElementById("weight-lb-group").style.display = "block";
      weightKg.required = false;
      weightLb.required = true;
    }
  });

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("bmi");
  if (savedValues) {
    heightUnit.value = savedValues.heightUnit;
    weightUnit.value = savedValues.weightUnit;

    if (savedValues.heightUnit === "cm") {
      heightCm.value = savedValues.heightCm;
      document.getElementById("height-cm-group").style.display = "block";
      document.getElementById("height-ft-in-group").style.display = "none";
      heightCm.required = true;
      heightFt.required = false;
      heightIn.required = false;
    } else {
      heightFt.value = savedValues.heightFt;
      heightIn.value = savedValues.heightIn;
      document.getElementById("height-cm-group").style.display = "none";
      document.getElementById("height-ft-in-group").style.display = "flex";
      heightCm.required = false;
      heightFt.required = true;
      heightIn.required = true;
    }

    if (savedValues.weightUnit === "kg") {
      weightKg.value = savedValues.weightKg;
      document.getElementById("weight-kg-group").style.display = "block";
      document.getElementById("weight-lb-group").style.display = "none";
      weightKg.required = true;
      weightLb.required = false;
    } else {
      weightLb.value = savedValues.weightLb;
      document.getElementById("weight-kg-group").style.display = "none";
      document.getElementById("weight-lb-group").style.display = "block";
      weightKg.required = false;
      weightLb.required = true;
    }
  } else {
    // Set default required attributes
    if (heightUnit.value === "cm") {
      heightCm.required = true;
      heightFt.required = false;
      heightIn.required = false;
    } else {
      heightCm.required = false;
      heightFt.required = true;
      heightIn.required = true;
    }

    if (weightUnit.value === "kg") {
      weightKg.required = true;
      weightLb.required = false;
    } else {
      weightKg.required = false;
      weightLb.required = true;
    }
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const heightUnitValue = heightUnit.value;
    const weightUnitValue = weightUnit.value;

    let heightValue, weightValue;

    // Validate height inputs
    if (heightUnitValue === "cm") {
      const heightCmValue = parseFloat(heightCm.value);
      const heightValidation = calculatorUtils.validateNumericInput(
        heightCmValue,
        50,
        250,
        "Please enter a valid height between 50 and 250 cm",
      );

      if (!heightValidation.valid) {
        calculatorUtils.showError(heightCm, heightValidation.message);
        return;
      }

      heightValue = heightCmValue / 100; // Convert cm to meters
    } else {
      const heightFtValue = parseInt(heightFt.value) || 0;
      const heightInValue = parseFloat(heightIn.value) || 0;

      const ftValidation = calculatorUtils.validateNumericInput(
        heightFtValue,
        1,
        8,
        "Please enter a valid height between 1 and 8 feet",
      );

      if (!ftValidation.valid) {
        calculatorUtils.showError(heightFt, ftValidation.message);
        return;
      }

      const inValidation = calculatorUtils.validateNumericInput(
        heightInValue,
        0,
        11.99,
        "Please enter a valid height between 0 and 11.99 inches",
      );

      if (!inValidation.valid) {
        calculatorUtils.showError(heightIn, inValidation.message);
        return;
      }

      // Convert feet and inches to meters
      heightValue = heightFtValue * 0.3048 + heightInValue * 0.0254;
    }

    // Validate weight inputs
    if (weightUnitValue === "kg") {
      const weightKgValue = parseFloat(weightKg.value);
      const weightValidation = calculatorUtils.validateNumericInput(
        weightKgValue,
        20,
        500,
        "Please enter a valid weight between 20 and 500 kg",
      );

      if (!weightValidation.valid) {
        calculatorUtils.showError(weightKg, weightValidation.message);
        return;
      }

      weightValue = weightKgValue;
    } else {
      const weightLbValue = parseFloat(weightLb.value);
      const weightValidation = calculatorUtils.validateNumericInput(
        weightLbValue,
        44,
        1100,
        "Please enter a valid weight between 44 and 1100 lb",
      );

      if (!weightValidation.valid) {
        calculatorUtils.showError(weightLb, weightValidation.message);
        return;
      }

      // Convert pounds to kilograms
      weightValue = weightLbValue * 0.453592;
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("bmi", {
      heightUnit: heightUnitValue,
      weightUnit: weightUnitValue,
      heightCm: heightUnitValue === "cm" ? parseFloat(heightCm.value) : null,
      heightFt: heightUnitValue === "ft" ? parseInt(heightFt.value) : null,
      heightIn: heightUnitValue === "ft" ? parseFloat(heightIn.value) : null,
      weightKg: weightUnitValue === "kg" ? parseFloat(weightKg.value) : null,
      weightLb: weightUnitValue === "lb" ? parseFloat(weightLb.value) : null,
    });

    // Calculate BMI
    const bmi = weightValue / (heightValue * heightValue);

    // Determine BMI category
    let category, description, healthRisk;

    if (bmi < 18.5) {
      category = "Underweight";
      description = "Your BMI indicates that you are underweight.";
      healthRisk =
        "Being underweight may indicate malnutrition or other health problems.";
    } else if (bmi >= 18.5 && bmi < 25) {
      category = "Normal weight";
      description = "Your BMI indicates that you have a healthy weight.";
      healthRisk = "Your weight is associated with the lowest health risks.";
    } else if (bmi >= 25 && bmi < 30) {
      category = "Overweight";
      description = "Your BMI indicates that you are overweight.";
      healthRisk =
        "Being overweight increases your risk of developing health problems.";
    } else if (bmi >= 30 && bmi < 35) {
      category = "Obesity Class I";
      description = "Your BMI indicates that you have Class I obesity.";
      healthRisk =
        "Obesity increases your risk of heart disease, diabetes, and other health problems.";
    } else if (bmi >= 35 && bmi < 40) {
      category = "Obesity Class II";
      description = "Your BMI indicates that you have Class II obesity.";
      healthRisk =
        "Class II obesity significantly increases your risk of serious health problems.";
    } else {
      category = "Obesity Class III";
      description = "Your BMI indicates that you have Class III obesity.";
      healthRisk =
        "Class III obesity is associated with very high risk of serious health problems.";
    }

    // Round BMI to 1 decimal place
    const roundedBMI = calculatorUtils.round(bmi, 1);

    // Display results
    document.getElementById("bmi-value").textContent = roundedBMI;
    document.getElementById("bmi-category").textContent = category;
    document.getElementById("bmi-description").textContent = description;
    document.getElementById("health-risk").textContent = healthRisk;
    results.style.display = "block";

    // Update BMI chart indicator
    updateBMIChartIndicator(roundedBMI);

    // Save calculation to history
    storageManager.saveCalculationHistory("bmi", {
      height: heightValue,
      weight: weightValue,
      bmi: roundedBMI,
      category: category,
    });

    // Show in-article ad after calculation
    const inArticleAd = document.getElementById(
      "ad-between-calculator-results",
    );
    if (inArticleAd) {
      inArticleAd.style.display = "block";
    }
  });
}

/**
 * Update BMI chart indicator
 */
function updateBMIChartIndicator(bmi) {
  const indicator = document.getElementById("bmi-indicator");
  if (!indicator) return;

  // Calculate position based on BMI value (clamp between 10 and 50)
  const clampedBMI = Math.max(10, Math.min(bmi, 50));
  const position = ((clampedBMI - 10) / 40) * 100;

  // Update indicator position
  indicator.style.left = `${position}%`;
}

/**
 * Initialize Calorie Calculator
 */
function initCalorieCalculator() {
  // Get form elements
  const form = document.getElementById("calorie-calculator-form");
  const gender = document.getElementById("gender");
  const age = document.getElementById("age");
  const heightUnit = document.getElementById("height-unit");
  const weightUnit = document.getElementById("weight-unit");
  const heightCm = document.getElementById("height-cm");
  const heightFt = document.getElementById("height-ft");
  const heightIn = document.getElementById("height-in");
  const weightKg = document.getElementById("weight-kg");
  const weightLb = document.getElementById("weight-lb");
  const activityLevel = document.getElementById("activity-level");
  const goal = document.getElementById("goal");
  const results = document.getElementById("calorie-results");

  // Handle unit change
  heightUnit.addEventListener("change", function () {
    if (this.value === "cm") {
      document.getElementById("height-cm-group").style.display = "block";
      document.getElementById("height-ft-in-group").style.display = "none";
      heightCm.required = true;
      heightFt.required = false;
      heightIn.required = false;
    } else {
      document.getElementById("height-cm-group").style.display = "none";
      document.getElementById("height-ft-in-group").style.display = "flex";
      heightCm.required = false;
      heightFt.required = true;
      heightIn.required = true;
    }
  });

  weightUnit.addEventListener("change", function () {
    if (this.value === "kg") {
      document.getElementById("weight-kg-group").style.display = "block";
      document.getElementById("weight-lb-group").style.display = "none";
      weightKg.required = true;
      weightLb.required = false;
    } else {
      document.getElementById("weight-kg-group").style.display = "none";
      document.getElementById("weight-lb-group").style.display = "block";
      weightKg.required = false;
      weightLb.required = true;
    }
  });

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("calorie");
  if (savedValues) {
    gender.value = savedValues.gender;
    age.value = savedValues.age;
    heightUnit.value = savedValues.heightUnit;
    weightUnit.value = savedValues.weightUnit;
    activityLevel.value = savedValues.activityLevel;
    goal.value = savedValues.goal;

    if (savedValues.heightUnit === "cm") {
      heightCm.value = savedValues.heightCm;
      document.getElementById("height-cm-group").style.display = "block";
      document.getElementById("height-ft-in-group").style.display = "none";
      heightCm.required = true;
      heightFt.required = false;
      heightIn.required = false;
    } else {
      heightFt.value = savedValues.heightFt;
      heightIn.value = savedValues.heightIn;
      document.getElementById("height-cm-group").style.display = "none";
      document.getElementById("height-ft-in-group").style.display = "flex";
      heightCm.required = false;
      heightFt.required = true;
      heightIn.required = true;
    }

    if (savedValues.weightUnit === "kg") {
      weightKg.value = savedValues.weightKg;
      document.getElementById("weight-kg-group").style.display = "block";
      document.getElementById("weight-lb-group").style.display = "none";
      weightKg.required = true;
      weightLb.required = false;
    } else {
      weightLb.value = savedValues.weightLb;
      document.getElementById("weight-kg-group").style.display = "none";
      document.getElementById("weight-lb-group").style.display = "block";
      weightKg.required = false;
      weightLb.required = true;
    }
  } else {
    // Set default required attributes
    if (heightUnit.value === "cm") {
      heightCm.required = true;
      heightFt.required = false;
      heightIn.required = false;
    } else {
      heightCm.required = false;
      heightFt.required = true;
      heightIn.required = true;
    }

    if (weightUnit.value === "kg") {
      weightKg.required = true;
      weightLb.required = false;
    } else {
      weightKg.required = false;
      weightLb.required = true;
    }
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const genderValue = gender.value;
    const ageValue = parseInt(age.value);
    const heightUnitValue = heightUnit.value;
    const weightUnitValue = weightUnit.value;
    const activityLevelValue = parseFloat(activityLevel.value);
    const goalValue = goal.value;

    // Validate age
    const ageValidation = calculatorUtils.validateNumericInput(
      ageValue,
      15,
      100,
      "Please enter a valid age between 15 and 100 years",
    );

    if (!ageValidation.valid) {
      calculatorUtils.showError(age, ageValidation.message);
      return;
    }

    let heightValue, weightValue;

    // Validate height inputs
    if (heightUnitValue === "cm") {
      const heightCmValue = parseFloat(heightCm.value);
      const heightValidation = calculatorUtils.validateNumericInput(
        heightCmValue,
        50,
        250,
        "Please enter a valid height between 50 and 250 cm",
      );

      if (!heightValidation.valid) {
        calculatorUtils.showError(heightCm, heightValidation.message);
        return;
      }

      heightValue = heightCmValue; // Keep in cm for BMR calculation
    } else {
      const heightFtValue = parseInt(heightFt.value) || 0;
      const heightInValue = parseFloat(heightIn.value) || 0;

      const ftValidation = calculatorUtils.validateNumericInput(
        heightFtValue,
        1,
        8,
        "Please enter a valid height between 1 and 8 feet",
      );

      if (!ftValidation.valid) {
        calculatorUtils.showError(heightFt, ftValidation.message);
        return;
      }

      const inValidation = calculatorUtils.validateNumericInput(
        heightInValue,
        0,
        11.99,
        "Please enter a valid height between 0 and 11.99 inches",
      );

      if (!inValidation.valid) {
        calculatorUtils.showError(heightIn, inValidation.message);
        return;
      }

      // Convert feet and inches to cm
      heightValue = heightFtValue * 30.48 + heightInValue * 2.54;
    }

    // Validate weight inputs
    if (weightUnitValue === "kg") {
      const weightKgValue = parseFloat(weightKg.value);
      const weightValidation = calculatorUtils.validateNumericInput(
        weightKgValue,
        20,
        500,
        "Please enter a valid weight between 20 and 500 kg",
      );

      if (!weightValidation.valid) {
        calculatorUtils.showError(weightKg, weightValidation.message);
        return;
      }

      weightValue = weightKgValue;
    } else {
      const weightLbValue = parseFloat(weightLb.value);
      const weightValidation = calculatorUtils.validateNumericInput(
        weightLbValue,
        44,
        1100,
        "Please enter a valid weight between 44 and 1100 lb",
      );

      if (!weightValidation.valid) {
        calculatorUtils.showError(weightLb, weightValidation.message);
        return;
      }

      // Convert pounds to kilograms
      weightValue = weightLbValue * 0.453592;
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("calorie", {
      gender: genderValue,
      age: ageValue,
      heightUnit: heightUnitValue,
      weightUnit: weightUnitValue,
      activityLevel: activityLevelValue,
      goal: goalValue,
      heightCm: heightUnitValue === "cm" ? parseFloat(heightCm.value) : null,
      heightFt: heightUnitValue === "ft" ? parseInt(heightFt.value) : null,
      heightIn: heightUnitValue === "ft" ? parseFloat(heightIn.value) : null,
      weightKg: weightUnitValue === "kg" ? parseFloat(weightKg.value) : null,
      weightLb: weightUnitValue === "lb" ? parseFloat(weightLb.value) : null,
    });

    // Calculate BMR using Mifflin-St Jeor Equation
    let bmr;
    if (genderValue === "male") {
      bmr = 10 * weightValue + 6.25 * heightValue - 5 * ageValue + 5;
    } else {
      bmr = 10 * weightValue + 6.25 * heightValue - 5 * ageValue - 161;
    }

    // Calculate TDEE (Total Daily Energy Expenditure)
    const tdee = bmr * activityLevelValue;

    // Calculate goal calories
    let goalCalories;
    let calorieDescription;

    switch (goalValue) {
      case "maintain":
        goalCalories = tdee;
        calorieDescription =
          "To maintain your current weight, consume approximately " +
          Math.round(goalCalories) +
          " calories per day.";
        break;
      case "mild-loss":
        goalCalories = tdee - 250;
        calorieDescription =
          "For mild weight loss (0.25 kg/week), consume approximately " +
          Math.round(goalCalories) +
          " calories per day.";
        break;
      case "weight-loss":
        goalCalories = tdee - 500;
        calorieDescription =
          "For weight loss (0.5 kg/week), consume approximately " +
          Math.round(goalCalories) +
          " calories per day.";
        break;
      case "extreme-loss":
        goalCalories = tdee - 1000;
        calorieDescription =
          "For extreme weight loss (1 kg/week), consume approximately " +
          Math.round(goalCalories) +
          " calories per day.";
        break;
      case "mild-gain":
        goalCalories = tdee + 250;
        calorieDescription =
          "For mild weight gain (0.25 kg/week), consume approximately " +
          Math.round(goalCalories) +
          " calories per day.";
        break;
      case "weight-gain":
        goalCalories = tdee + 500;
        calorieDescription =
          "For weight gain (0.5 kg/week), consume approximately " +
          Math.round(goalCalories) +
          " calories per day.";
        break;
      default:
        goalCalories = tdee;
        calorieDescription =
          "To maintain your current weight, consume approximately " +
          Math.round(goalCalories) +
          " calories per day.";
    }

    // Calculate macronutrient breakdown (40% carbs, 30% protein, 30% fat)
    const proteinCalories = goalCalories * 0.3;
    const carbsCalories = goalCalories * 0.4;
    const fatCalories = goalCalories * 0.3;

    const proteinGrams = proteinCalories / 4; // 4 calories per gram of protein
    const carbsGrams = carbsCalories / 4; // 4 calories per gram of carbs
    const fatGrams = fatCalories / 9; // 9 calories per gram of fat

    // Round values
    const roundedBMR = Math.round(bmr);
    const roundedTDEE = Math.round(tdee);
    const roundedGoalCalories = Math.round(goalCalories);
    const roundedProteinGrams = Math.round(proteinGrams);
    const roundedCarbsGrams = Math.round(carbsGrams);
    const roundedFatGrams = Math.round(fatGrams);

    // Display results
    document.getElementById("bmr-value").textContent =
      roundedBMR + " calories/day";
    document.getElementById("calorie-value").textContent =
      roundedTDEE + " calories/day";
    document.getElementById("goal-calories").textContent =
      roundedGoalCalories + " calories/day";
    document.getElementById("calorie-description").textContent =
      calorieDescription;

    // Update macronutrient values
    document.getElementById("protein-value").textContent =
      roundedProteinGrams + "g";
    document.getElementById("carbs-value").textContent =
      roundedCarbsGrams + "g";
    document.getElementById("fat-value").textContent = roundedFatGrams + "g";

    document.getElementById("protein-percent").textContent = "30%";
    document.getElementById("carbs-percent").textContent = "40%";
    document.getElementById("fat-percent").textContent = "30%";

    // Update macronutrient chart
    document.getElementById("protein-segment").style.width = "30%";
    document.getElementById("carbs-segment").style.width = "40%";
    document.getElementById("fat-segment").style.width = "30%";

    // Show results
    results.style.display = "block";

    // Save calculation to history
    storageManager.saveCalculationHistory("calorie", {
      gender: genderValue,
      age: ageValue,
      height: heightValue,
      weight: weightValue,
      activityLevel: activityLevelValue,
      goal: goalValue,
      bmr: roundedBMR,
      tdee: roundedTDEE,
      goalCalories: roundedGoalCalories,
    });
  });
}

/**
 * Initialize Pregnancy Due Date Calculator
 */
function initPregnancyCalculator() {
  // Get form elements
  const form = document.getElementById("pregnancy-calculator-form");
  const calculationMethod = document.getElementById("calculation-method");
  const lmpDate = document.getElementById("lmp-date");
  const conceptionDate = document.getElementById("conception-date");
  const ultrasoundDate = document.getElementById("ultrasound-date");
  const ultrasoundWeeks = document.getElementById("ultrasound-weeks");
  const ultrasoundDays = document.getElementById("ultrasound-days");
  const cycleLength = document.getElementById("cycle-length");
  const results = document.getElementById("pregnancy-results");

  // Handle calculation method change
  calculationMethod.addEventListener("change", function () {
    if (this.value === "lmp") {
      document.getElementById("lmp-date-group").style.display = "block";
      document.getElementById("conception-date-group").style.display = "none";
      document.getElementById("ultrasound-date-group").style.display = "none";
      document.getElementById("ultrasound-weeks-group").style.display = "none";
      document.getElementById("ultrasound-days-group").style.display = "none";
      document.getElementById("cycle-length-group").style.display = "block";
    } else if (this.value === "conception") {
      document.getElementById("lmp-date-group").style.display = "none";
      document.getElementById("conception-date-group").style.display = "block";
      document.getElementById("ultrasound-date-group").style.display = "none";
      document.getElementById("ultrasound-weeks-group").style.display = "none";
      document.getElementById("ultrasound-days-group").style.display = "none";
      document.getElementById("cycle-length-group").style.display = "none";
    } else if (this.value === "ultrasound") {
      document.getElementById("lmp-date-group").style.display = "none";
      document.getElementById("conception-date-group").style.display = "none";
      document.getElementById("ultrasound-date-group").style.display = "block";
      document.getElementById("ultrasound-weeks-group").style.display = "block";
      document.getElementById("ultrasound-days-group").style.display = "block";
      document.getElementById("cycle-length-group").style.display = "none";
    }
  });

  // Set today's date as the default date
  const today = new Date();
  const formattedDate = today.toISOString().split("T")[0];
  lmpDate.value = formattedDate;
  conceptionDate.value = formattedDate;
  ultrasoundDate.value = formattedDate;

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("pregnancy");
  if (savedValues) {
    calculationMethod.value = savedValues.calculationMethod;
    cycleLength.value = savedValues.cycleLength;

    if (savedValues.lmpDate) {
      lmpDate.value = savedValues.lmpDate;
    }

    if (savedValues.conceptionDate) {
      conceptionDate.value = savedValues.conceptionDate;
    }

    if (savedValues.ultrasoundDate) {
      ultrasoundDate.value = savedValues.ultrasoundDate;
    }

    if (savedValues.ultrasoundWeeks) {
      ultrasoundWeeks.value = savedValues.ultrasoundWeeks;
    }

    if (savedValues.ultrasoundDays) {
      ultrasoundDays.value = savedValues.ultrasoundDays;
    }

    // Trigger change event to show/hide appropriate fields
    const event = new Event("change");
    calculationMethod.dispatchEvent(event);
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const calculationMethodValue = calculationMethod.value;
    const cycleLengthValue = parseInt(cycleLength.value);

    // Validate cycle length
    if (calculationMethodValue === "lmp") {
      const cycleLengthValidation = calculatorUtils.validateNumericInput(
        cycleLengthValue,
        21,
        45,
        "Please enter a valid cycle length between 21 and 45 days",
      );

      if (!cycleLengthValidation.valid) {
        calculatorUtils.showError(cycleLength, cycleLengthValidation.message);
        return;
      }
    }

    // Calculate due date based on calculation method
    let dueDate, conceptionDateValue;

    if (calculationMethodValue === "lmp") {
      // Calculate due date based on LMP (Naegele's rule)
      const lmpDateValue = new Date(lmpDate.value);

      // Validate LMP date
      if (isNaN(lmpDateValue.getTime())) {
        calculatorUtils.showError(lmpDate, "Please enter a valid date");
        return;
      }

      // Calculate conception date (LMP + 14 days, adjusted for cycle length)
      const ovulationDay = cycleLengthValue - 14;
      conceptionDateValue = new Date(lmpDateValue);
      conceptionDateValue.setDate(lmpDateValue.getDate() + ovulationDay);

      // Calculate due date (LMP + 280 days, adjusted for cycle length)
      dueDate = new Date(lmpDateValue);
      dueDate.setDate(lmpDateValue.getDate() + 280 - (28 - cycleLengthValue));
    } else if (calculationMethodValue === "conception") {
      // Calculate due date based on conception date
      conceptionDateValue = new Date(conceptionDate.value);

      // Validate conception date
      if (isNaN(conceptionDateValue.getTime())) {
        calculatorUtils.showError(conceptionDate, "Please enter a valid date");
        return;
      }

      // Calculate due date (conception date + 266 days)
      dueDate = new Date(conceptionDateValue);
      dueDate.setDate(conceptionDateValue.getDate() + 266);
    } else if (calculationMethodValue === "ultrasound") {
      // Calculate due date based on ultrasound
      const ultrasoundDateValue = new Date(ultrasoundDate.value);
      const ultrasoundWeeksValue = parseInt(ultrasoundWeeks.value) || 0;
      const ultrasoundDaysValue = parseInt(ultrasoundDays.value) || 0;

      // Validate ultrasound date
      if (isNaN(ultrasoundDateValue.getTime())) {
        calculatorUtils.showError(ultrasoundDate, "Please enter a valid date");
        return;
      }

      // Validate ultrasound weeks
      const weeksValidation = calculatorUtils.validateNumericInput(
        ultrasoundWeeksValue,
        1,
        40,
        "Please enter a valid number of weeks between 1 and 40",
      );

      if (!weeksValidation.valid) {
        calculatorUtils.showError(ultrasoundWeeks, weeksValidation.message);
        return;
      }

      // Validate ultrasound days
      const daysValidation = calculatorUtils.validateNumericInput(
        ultrasoundDaysValue,
        0,
        6,
        "Please enter a valid number of days between 0 and 6",
      );

      if (!daysValidation.valid) {
        calculatorUtils.showError(ultrasoundDays, daysValidation.message);
        return;
      }

      // Calculate total days of pregnancy at ultrasound
      const totalDaysAtUltrasound =
        ultrasoundWeeksValue * 7 + ultrasoundDaysValue;

      // Calculate conception date (ultrasound date - total days)
      conceptionDateValue = new Date(ultrasoundDateValue);
      conceptionDateValue.setDate(
        ultrasoundDateValue.getDate() - totalDaysAtUltrasound,
      );

      // Calculate due date (conception date + 266 days)
      dueDate = new Date(conceptionDateValue);
      dueDate.setDate(conceptionDateValue.getDate() + 266);
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("pregnancy", {
      calculationMethod: calculationMethodValue,
      lmpDate: lmpDate.value,
      conceptionDate: conceptionDate.value,
      ultrasoundDate: ultrasoundDate.value,
      ultrasoundWeeks: ultrasoundWeeks.value,
      ultrasoundDays: ultrasoundDays.value,
      cycleLength: cycleLength.value,
    });

    // Calculate current pregnancy week
    const today = new Date();
    const daysSinceConception = Math.floor(
      (today - conceptionDateValue) / (1000 * 60 * 60 * 24),
    );
    const currentWeek = Math.floor(daysSinceConception / 7);
    const currentDay = daysSinceConception % 7;

    // Calculate trimester dates
    const firstTrimesterEnd = new Date(conceptionDateValue);
    firstTrimesterEnd.setDate(conceptionDateValue.getDate() + 13 * 7); // 13 weeks after conception

    const secondTrimesterEnd = new Date(conceptionDateValue);
    secondTrimesterEnd.setDate(conceptionDateValue.getDate() + 26 * 7); // 26 weeks after conception

    // Format dates
    const formatDate = (date) => {
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    };

    // Calculate pregnancy progress percentage
    let progressPercentage = (daysSinceConception / 280) * 100;
    progressPercentage = Math.max(0, Math.min(progressPercentage, 100)); // Clamp between 0 and 100

    // Determine pregnancy description based on current week
    let pregnancyDescription;

    if (currentWeek < 0) {
      pregnancyDescription =
        "Based on the information provided, conception hasn't occurred yet.";
      progressPercentage = 0;
    } else if (currentWeek < 13) {
      pregnancyDescription = `You are in your first trimester. During this time, your baby's major organs and systems are forming.`;
    } else if (currentWeek < 27) {
      pregnancyDescription = `You are in your second trimester. During this time, your baby is growing rapidly and you may start to feel movement.`;
    } else if (currentWeek < 40) {
      pregnancyDescription = `You are in your third trimester. Your baby is continuing to grow and develop, preparing for birth.`;
    } else {
      pregnancyDescription = `You have passed your due date. Consult with your healthcare provider about next steps.`;
      progressPercentage = 100;
    }

    // Display results
    document.getElementById("due-date").textContent = formatDate(dueDate);
    document.getElementById("current-week").textContent =
      currentWeek >= 0
        ? `${currentWeek} weeks, ${currentDay} days`
        : "Not yet pregnant";
    document.getElementById("first-trimester").textContent = `${formatDate(
      conceptionDateValue,
    )} to ${formatDate(firstTrimesterEnd)}`;
    document.getElementById("second-trimester").textContent = `${formatDate(
      new Date(firstTrimesterEnd.getTime() + 24 * 60 * 60 * 1000),
    )} to ${formatDate(secondTrimesterEnd)}`;
    document.getElementById("third-trimester").textContent = `${formatDate(
      new Date(secondTrimesterEnd.getTime() + 24 * 60 * 60 * 1000),
    )} to ${formatDate(dueDate)}`;
    document.getElementById("pregnancy-description").textContent =
      pregnancyDescription;

    // Update pregnancy timeline progress
    document.getElementById(
      "pregnancy-progress",
    ).style.width = `${progressPercentage}%`;

    // Show results
    results.style.display = "block";

    // Save calculation to history
    storageManager.saveCalculationHistory("pregnancy", {
      calculationMethod: calculationMethodValue,
      dueDate: formatDate(dueDate),
      currentWeek:
        currentWeek >= 0
          ? `${currentWeek} weeks, ${currentDay} days`
          : "Not yet pregnant",
    });
  });
}

/**
 * Initialize Body Fat Percentage Calculator
 */
function initBodyFatCalculator() {
  // Get form elements
  const form = document.getElementById("body-fat-calculator-form");
  const calculationMethod = document.getElementById("calculation-method");
  const gender = document.getElementById("gender");
  const age = document.getElementById("age");
  const heightUnit = document.getElementById("height-unit");
  const weightUnit = document.getElementById("weight-unit");
  const heightCm = document.getElementById("height-cm");
  const heightFt = document.getElementById("height-ft");
  const heightIn = document.getElementById("height-in");
  const weightKg = document.getElementById("weight-kg");
  const weightLb = document.getElementById("weight-lb");
  const neck = document.getElementById("neck");
  const waist = document.getElementById("waist");
  const hip = document.getElementById("hip");
  const triceps = document.getElementById("triceps");
  const subscapular = document.getElementById("subscapular");
  const suprailiac = document.getElementById("suprailiac");
  const thigh = document.getElementById("thigh");
  const results = document.getElementById("body-fat-results");

  // Handle calculation method change
  calculationMethod.addEventListener("change", function () {
    if (this.value === "navy") {
      document.getElementById("navy-measurements").style.display = "block";
      document.getElementById("skinfold-measurements").style.display = "none";
    } else if (this.value === "skinfold") {
      document.getElementById("navy-measurements").style.display = "none";
      document.getElementById("skinfold-measurements").style.display = "block";
    } else if (this.value === "bmi") {
      document.getElementById("navy-measurements").style.display = "none";
      document.getElementById("skinfold-measurements").style.display = "none";
    }
  });

  // Handle gender change
  gender.addEventListener("change", function () {
    if (this.value === "male") {
      document.getElementById("hip-group").style.display = "none";
      document.getElementById("thigh-group").style.display = "none";
    } else {
      document.getElementById("hip-group").style.display = "block";
      document.getElementById("thigh-group").style.display = "block";
    }
  });

  // Handle unit change
  heightUnit.addEventListener("change", function () {
    if (this.value === "cm") {
      document.getElementById("height-cm-group").style.display = "block";
      document.getElementById("height-ft-in-group").style.display = "none";
      heightCm.required = true;
      heightFt.required = false;
      heightIn.required = false;
    } else {
      document.getElementById("height-cm-group").style.display = "none";
      document.getElementById("height-ft-in-group").style.display = "flex";
      heightCm.required = false;
      heightFt.required = true;
      heightIn.required = true;
    }
  });

  weightUnit.addEventListener("change", function () {
    if (this.value === "kg") {
      document.getElementById("weight-kg-group").style.display = "block";
      document.getElementById("weight-lb-group").style.display = "none";
      weightKg.required = true;
      weightLb.required = false;
    } else {
      document.getElementById("weight-kg-group").style.display = "none";
      document.getElementById("weight-lb-group").style.display = "block";
      weightKg.required = false;
      weightLb.required = true;
    }
  });

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("body-fat");
  if (savedValues) {
    calculationMethod.value = savedValues.calculationMethod;
    gender.value = savedValues.gender;
    age.value = savedValues.age;
    heightUnit.value = savedValues.heightUnit;
    weightUnit.value = savedValues.weightUnit;

    if (savedValues.heightUnit === "cm") {
      heightCm.value = savedValues.heightCm;
      document.getElementById("height-cm-group").style.display = "block";
      document.getElementById("height-ft-in-group").style.display = "none";
      heightCm.required = true;
      heightFt.required = false;
      heightIn.required = false;
    } else {
      heightFt.value = savedValues.heightFt;
      heightIn.value = savedValues.heightIn;
      document.getElementById("height-cm-group").style.display = "none";
      document.getElementById("height-ft-in-group").style.display = "flex";
      heightCm.required = false;
      heightFt.required = true;
      heightIn.required = true;
    }

    if (savedValues.weightUnit === "kg") {
      weightKg.value = savedValues.weightKg;
      document.getElementById("weight-kg-group").style.display = "block";
      document.getElementById("weight-lb-group").style.display = "none";
      weightKg.required = true;
      weightLb.required = false;
    } else {
      weightLb.value = savedValues.weightLb;
      document.getElementById("weight-kg-group").style.display = "none";
      document.getElementById("weight-lb-group").style.display = "block";
      weightKg.required = false;
      weightLb.required = true;
    }

    if (savedValues.neck) neck.value = savedValues.neck;
    if (savedValues.waist) waist.value = savedValues.waist;
    if (savedValues.hip) hip.value = savedValues.hip;
    if (savedValues.triceps) triceps.value = savedValues.triceps;
    if (savedValues.subscapular) subscapular.value = savedValues.subscapular;
    if (savedValues.suprailiac) suprailiac.value = savedValues.suprailiac;
    if (savedValues.thigh) thigh.value = savedValues.thigh;

    // Trigger change events to show/hide appropriate fields
    const methodEvent = new Event("change");
    calculationMethod.dispatchEvent(methodEvent);

    const genderEvent = new Event("change");
    gender.dispatchEvent(genderEvent);
  } else {
    // Set default required attributes
    if (heightUnit.value === "cm") {
      heightCm.required = true;
      heightFt.required = false;
      heightIn.required = false;
    } else {
      heightCm.required = false;
      heightFt.required = true;
      heightIn.required = true;
    }

    if (weightUnit.value === "kg") {
      weightKg.required = true;
      weightLb.required = false;
    } else {
      weightKg.required = false;
      weightLb.required = true;
    }

    // Trigger change events to show/hide appropriate fields
    const methodEvent = new Event("change");
    calculationMethod.dispatchEvent(methodEvent);

    const genderEvent = new Event("change");
    gender.dispatchEvent(genderEvent);
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const calculationMethodValue = calculationMethod.value;
    const genderValue = gender.value;
    const ageValue = parseInt(age.value);
    const heightUnitValue = heightUnit.value;
    const weightUnitValue = weightUnit.value;

    // Validate age
    const ageValidation = calculatorUtils.validateNumericInput(
      ageValue,
      15,
      100,
      "Please enter a valid age between 15 and 100 years",
    );

    if (!ageValidation.valid) {
      calculatorUtils.showError(age, ageValidation.message);
      return;
    }

    let heightValue, weightValue;

    // Validate height inputs
    if (heightUnitValue === "cm") {
      const heightCmValue = parseFloat(heightCm.value);
      const heightValidation = calculatorUtils.validateNumericInput(
        heightCmValue,
        50,
        250,
        "Please enter a valid height between 50 and 250 cm",
      );

      if (!heightValidation.valid) {
        calculatorUtils.showError(heightCm, heightValidation.message);
        return;
      }

      heightValue = heightCmValue;
    } else {
      const heightFtValue = parseInt(heightFt.value) || 0;
      const heightInValue = parseFloat(heightIn.value) || 0;

      const ftValidation = calculatorUtils.validateNumericInput(
        heightFtValue,
        1,
        8,
        "Please enter a valid height between 1 and 8 feet",
      );

      if (!ftValidation.valid) {
        calculatorUtils.showError(heightFt, ftValidation.message);
        return;
      }

      const inValidation = calculatorUtils.validateNumericInput(
        heightInValue,
        0,
        11.99,
        "Please enter a valid height between 0 and 11.99 inches",
      );

      if (!inValidation.valid) {
        calculatorUtils.showError(heightIn, inValidation.message);
        return;
      }

      // Convert feet and inches to cm
      heightValue = heightFtValue * 30.48 + heightInValue * 2.54;
    }

    // Validate weight inputs
    if (weightUnitValue === "kg") {
      const weightKgValue = parseFloat(weightKg.value);
      const weightValidation = calculatorUtils.validateNumericInput(
        weightKgValue,
        20,
        500,
        "Please enter a valid weight between 20 and 500 kg",
      );

      if (!weightValidation.valid) {
        calculatorUtils.showError(weightKg, weightValidation.message);
        return;
      }

      weightValue = weightKgValue;
    } else {
      const weightLbValue = parseFloat(weightLb.value);
      const weightValidation = calculatorUtils.validateNumericInput(
        weightLbValue,
        44,
        1100,
        "Please enter a valid weight between 44 and 1100 lb",
      );

      if (!weightValidation.valid) {
        calculatorUtils.showError(weightLb, weightValidation.message);
        return;
      }

      // Convert pounds to kilograms
      weightValue = weightLbValue * 0.453592;
    }

    // Calculate body fat percentage based on method
    let bodyFatPercentage;

    if (calculationMethodValue === "navy") {
      // Navy method (circumference measurements)
      const neckValue = parseFloat(neck.value);
      const waistValue = parseFloat(waist.value);

      // Validate neck circumference
      const neckValidation = calculatorUtils.validateNumericInput(
        neckValue,
        20,
        100,
        "Please enter a valid neck circumference between 20 and 100 cm",
      );

      if (!neckValidation.valid) {
        calculatorUtils.showError(neck, neckValidation.message);
        return;
      }

      // Validate waist circumference
      const waistValidation = calculatorUtils.validateNumericInput(
        waistValue,
        40,
        200,
        "Please enter a valid waist circumference between 40 and 200 cm",
      );

      if (!waistValidation.valid) {
        calculatorUtils.showError(waist, waistValidation.message);
        return;
      }

      if (genderValue === "male") {
        // Navy formula for men
        bodyFatPercentage =
          495 /
            (1.0324 -
              0.19077 * Math.log10(waistValue - neckValue) +
              0.15456 * Math.log10(heightValue)) -
          450;
      } else {
        // Navy formula for women (requires hip measurement)
        const hipValue = parseFloat(hip.value);

        // Validate hip circumference
        const hipValidation = calculatorUtils.validateNumericInput(
          hipValue,
          40,
          200,
          "Please enter a valid hip circumference between 40 and 200 cm",
        );

        if (!hipValidation.valid) {
          calculatorUtils.showError(hip, hipValidation.message);
          return;
        }

        bodyFatPercentage =
          495 /
            (1.29579 -
              0.35004 * Math.log10(waistValue + hipValue - neckValue) +
              0.221 * Math.log10(heightValue)) -
          450;
      }
    } else if (calculationMethodValue === "skinfold") {
      // Skinfold method
      const tricepsValue = parseFloat(triceps.value);
      const subscapularValue = parseFloat(subscapular.value);
      const suprailiacValue = parseFloat(suprailiac.value);

      // Validate triceps skinfold
      const tricepsValidation = calculatorUtils.validateNumericInput(
        tricepsValue,
        1,
        100,
        "Please enter a valid triceps skinfold between 1 and 100 mm",
      );

      if (!tricepsValidation.valid) {
        calculatorUtils.showError(triceps, tricepsValidation.message);
        return;
      }

      // Validate subscapular skinfold
      const subscapularValidation = calculatorUtils.validateNumericInput(
        subscapularValue,
        1,
        100,
        "Please enter a valid subscapular skinfold between 1 and 100 mm",
      );

      if (!subscapularValidation.valid) {
        calculatorUtils.showError(subscapular, subscapularValidation.message);
        return;
      }

      // Validate suprailiac skinfold
      const suprailiacValidation = calculatorUtils.validateNumericInput(
        suprailiacValue,
        1,
        100,
        "Please enter a valid suprailiac skinfold between 1 and 100 mm",
      );

      if (!suprailiacValidation.valid) {
        calculatorUtils.showError(suprailiac, suprailiacValidation.message);
        return;
      }

      if (genderValue === "male") {
        // Jackson-Pollock 3-site formula for men
        const sum = tricepsValue + subscapularValue + suprailiacValue;
        const bodyDensity =
          1.10938 -
          0.0008267 * sum +
          0.0000016 * sum * sum -
          0.0002574 * ageValue;
        bodyFatPercentage = (4.95 / bodyDensity - 4.5) * 100;
      } else {
        // Jackson-Pollock 4-site formula for women (requires thigh measurement)
        const thighValue = parseFloat(thigh.value);

        // Validate thigh skinfold
        const thighValidation = calculatorUtils.validateNumericInput(
          thighValue,
          1,
          100,
          "Please enter a valid thigh skinfold between 1 and 100 mm",
        );

        if (!thighValidation.valid) {
          calculatorUtils.showError(thigh, thighValidation.message);
          return;
        }

        const sum =
          tricepsValue + subscapularValue + suprailiacValue + thighValue;
        const bodyDensity =
          1.096095 -
          0.0006952 * sum +
          0.0000011 * sum * sum -
          0.0000714 * ageValue;
        bodyFatPercentage = (4.95 / bodyDensity - 4.5) * 100;
      }
    } else if (calculationMethodValue === "bmi") {
      // BMI method (less accurate)
      const bmi = weightValue / ((heightValue / 100) * (heightValue / 100));

      if (genderValue === "male") {
        // Deurenberg formula for men
        bodyFatPercentage = 1.2 * bmi + 0.23 * ageValue - 10.8 * 1 - 5.4;
      } else {
        // Deurenberg formula for women
        bodyFatPercentage = 1.2 * bmi + 0.23 * ageValue - 10.8 * 0 - 5.4;
      }
    }

    // Ensure body fat percentage is within reasonable limits
    bodyFatPercentage = Math.max(1, Math.min(bodyFatPercentage, 60));

    // Calculate fat mass and lean mass
    const fatMass = (bodyFatPercentage / 100) * weightValue;
    const leanMass = weightValue - fatMass;

    // Determine body fat category
    let category, description;

    if (genderValue === "male") {
      if (bodyFatPercentage < 6) {
        category = "Essential Fat";
        description =
          "This is the minimum level of fat needed for basic physical and physiological health. It is not sustainable for most men to maintain this level long-term.";
      } else if (bodyFatPercentage < 14) {
        category = "Athletic";
        description =
          "This range is typical for athletes and those with very active lifestyles. It shows excellent fitness and a high level of discipline with diet and exercise.";
      } else if (bodyFatPercentage < 18) {
        category = "Fitness";
        description =
          "This range indicates a fit and healthy body with good muscle definition. It is achievable for most men through consistent exercise and proper nutrition.";
      } else if (bodyFatPercentage < 25) {
        category = "Average";
        description =
          "This is the typical range for the average adult man. While not optimal for health, it is not associated with high health risks.";
      } else {
        category = "Obese";
        description =
          "This range indicates obesity and is associated with increased health risks including heart disease, diabetes, and other metabolic conditions.";
      }
    } else {
      if (bodyFatPercentage < 16) {
        category = "Essential Fat";
        description =
          "This is the minimum level of fat needed for basic physical and physiological health. It is not sustainable for most women to maintain this level long-term.";
      } else if (bodyFatPercentage < 24) {
        category = "Athletic";
        description =
          "This range is typical for female athletes and those with very active lifestyles. It shows excellent fitness and a high level of discipline with diet and exercise.";
      } else if (bodyFatPercentage < 30) {
        category = "Fitness";
        description =
          "This range indicates a fit and healthy body with good muscle definition. It is achievable for most women through consistent exercise and proper nutrition.";
      } else if (bodyFatPercentage < 32) {
        category = "Average";
        description =
          "This is the typical range for the average adult woman. While not optimal for health, it is not associated with high health risks.";
      } else {
        category = "Obese";
        description =
          "This range indicates obesity and is associated with increased health risks including heart disease, diabetes, and other metabolic conditions.";
      }
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("body-fat", {
      calculationMethod: calculationMethodValue,
      gender: genderValue,
      age: ageValue,
      heightUnit: heightUnitValue,
      weightUnit: weightUnitValue,
      heightCm: heightUnitValue === "cm" ? parseFloat(heightCm.value) : null,
      heightFt: heightUnitValue === "ft" ? parseInt(heightFt.value) : null,
      heightIn: heightUnitValue === "ft" ? parseFloat(heightIn.value) : null,
      weightKg: weightUnitValue === "kg" ? parseFloat(weightKg.value) : null,
      weightLb: weightUnitValue === "lb" ? parseFloat(weightLb.value) : null,
      neck: neck.value,
      waist: waist.value,
      hip: hip.value,
      triceps: triceps.value,
      subscapular: subscapular.value,
      suprailiac: suprailiac.value,
      thigh: thigh.value,
    });

    // Round values
    const roundedBodyFatPercentage = calculatorUtils.round(
      bodyFatPercentage,
      1,
    );
    const roundedFatMass = calculatorUtils.round(fatMass, 1);
    const roundedLeanMass = calculatorUtils.round(leanMass, 1);

    // Display results
    document.getElementById("body-fat-value").textContent =
      roundedBodyFatPercentage + "%";
    document.getElementById("fat-mass").textContent = roundedFatMass + " kg";
    document.getElementById("lean-mass").textContent = roundedLeanMass + " kg";
    document.getElementById("body-fat-category").textContent = category;
    document.getElementById("body-fat-description").textContent = description;

    // Update body fat indicator position
    updateBodyFatIndicator(roundedBodyFatPercentage, genderValue);

    // Show results
    results.style.display = "block";

    // Save calculation to history
    storageManager.saveCalculationHistory("body-fat", {
      method: calculationMethodValue,
      gender: genderValue,
      age: ageValue,
      height: heightValue,
      weight: weightValue,
      bodyFatPercentage: roundedBodyFatPercentage,
      category: category,
    });
  });
}

/**
 * Update Body Fat chart indicator
 */
function updateBodyFatIndicator(bodyFatPercentage, gender) {
  const indicator = document.getElementById("body-fat-indicator");
  if (!indicator) return;

  // Calculate position based on body fat percentage and gender
  let position;

  if (gender === "male") {
    // For men: Scale from 2% to 40%
    const clampedBF = Math.max(2, Math.min(bodyFatPercentage, 40));
    position = ((clampedBF - 2) / 38) * 100;
  } else {
    // For women: Scale from 10% to 50%
    const clampedBF = Math.max(10, Math.min(bodyFatPercentage, 50));
    position = ((clampedBF - 10) / 40) * 100;
  }

  // Update indicator position
  indicator.style.left = `${position}%`;
}
