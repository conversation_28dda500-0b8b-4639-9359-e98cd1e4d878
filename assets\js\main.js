/**
 * Main JavaScript file for CalculatorSuites
 */

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
  // Initialize print functionality
  initPrintFunctionality();

  // Initialize scroll to top button
  initScrollToTop();
});

/**
 * Initialize print functionality for calculator results
 */
function initPrintFunctionality() {
  const printButtons = document.querySelectorAll(".print-results-btn");

  printButtons.forEach((button) => {
    button.addEventListener("click", function (e) {
      e.preventDefault();

      // Get the results container
      const resultsContainer = this.closest(
        ".calculator-container",
      ).querySelector(".results");

      if (resultsContainer) {
        // Create a new window for printing
        const printWindow = window.open("", "_blank");

        // Get the calculator title
        const calculatorTitle = this.closest(
          ".calculator-container",
        ).querySelector("h2").textContent;

        // Create print content
        printWindow.document.write(`
          <!DOCTYPE html>
          <html>
          <head>
            <title>${calculatorTitle} Results</title>
            <style>
              body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
              }
              h1 {
                color: #4361ee;
                border-bottom: 2px solid #e9ecef;
                padding-bottom: 10px;
              }
              .result-row {
                display: flex;
                justify-content: space-between;
                padding: 10px 0;
                border-bottom: 1px solid #e9ecef;
              }
              .highlight {
                font-weight: bold;
                background-color: #f8f9fa;
                padding: 10px;
                border-radius: 4px;
                margin: 10px 0;
              }
              .footer {
                margin-top: 30px;
                font-size: 12px;
                color: #6c757d;
                text-align: center;
              }
            </style>
          </head>
          <body>
            <h1>${calculatorTitle} Results</h1>
            ${resultsContainer.innerHTML}
            <div class="footer">
              <p>Generated by CalculatorSuites | www.calculatorsuites.com</p>
              <p>Date: ${new Date().toLocaleDateString()}</p>
            </div>
            <script>
              window.onload = function() { window.print(); }
            </script>
          </body>
          </html>
        `);

        printWindow.document.close();
      }
    });
  });
}

/**
 * Initialize scroll to top button
 */
function initScrollToTop() {
  // Create scroll to top button if it doesn't exist
  let scrollToTopBtn = document.querySelector(".scroll-to-top");

  if (!scrollToTopBtn) {
    scrollToTopBtn = document.createElement("button");
    scrollToTopBtn.className = "scroll-to-top";
    scrollToTopBtn.innerHTML = "&uarr;";
    scrollToTopBtn.setAttribute("aria-label", "Scroll to top");
    scrollToTopBtn.style.display = "none";
    document.body.appendChild(scrollToTopBtn);
  }

  // Show/hide button based on scroll position
  window.addEventListener("scroll", function () {
    if (window.pageYOffset > 300) {
      scrollToTopBtn.style.display = "block";
    } else {
      scrollToTopBtn.style.display = "none";
    }
  });

  // Scroll to top when button is clicked
  scrollToTopBtn.addEventListener("click", function () {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  });
}
