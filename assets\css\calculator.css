/* Calculator-specific styles */

/* Calculator Container */
.calculator-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
  width: 100%;
  box-sizing: border-box;
}

.calculator-container h2 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--neutral-200);
  font-size: 1.5rem;
  word-wrap: break-word;
}

/* Form Elements */
.form-group {
  margin-bottom: 1.25rem;
  width: 100%;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--neutral-700);
}

.form-group input[type="number"],
.form-group input[type="text"],
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--neutral-300);
  border-radius: 0.25rem;
  font-size: var(--text-base);
  transition: border-color 0.2s ease;
  box-sizing: border-box;
  -webkit-appearance: none;
  /* Removes default styling on iOS */
  appearance: none;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

/* Custom select arrow for better mobile appearance */
select {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23495057' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  padding-right: 2.5rem;
}

.input-group {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  /* Allow wrapping on very small screens */
}

.input-group input,
.input-group select {
  flex: 1;
  min-width: 120px;
  /* Prevent inputs from becoming too narrow */
}

.calculate-btn {
  display: block;
  width: 100%;
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-size: var(--text-base);
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  -webkit-tap-highlight-color: transparent;
  /* Remove tap highlight on mobile */
  margin-top: 1.5rem;
}

.calculate-btn:hover {
  background-color: var(--primary-dark);
}

/* Active state for better mobile feedback */
.calculate-btn:active {
  transform: translateY(1px);
  background-color: var(--primary-dark);
}

/* Unit Toggle */
.unit-toggle {
  display: flex;
  margin-bottom: 1.5rem;
  background-color: var(--neutral-100);
  border-radius: 0.25rem;
  padding: 0.25rem;
}

.unit-toggle label {
  flex: 1;
  text-align: center;
  padding: 0.5rem;
  cursor: pointer;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
}

.unit-toggle input[type="radio"] {
  position: absolute;
  opacity: 0;
}

.unit-toggle input[type="radio"]:checked+label {
  background-color: var(--primary-color);
  color: white;
}

/* Results Display */
.results {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--neutral-300);
  width: 100%;
  box-sizing: border-box;
}

.results h3 {
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.result-row {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--neutral-200);
  flex-wrap: wrap;
  /* Allow wrapping on very small screens */
}

.result-row>span:first-child {
  margin-right: 1rem;
  font-weight: 500;
}

.result-row>span:last-child {
  font-weight: 600;
  text-align: right;
}

.result-row.highlight {
  font-weight: 700;
  font-size: var(--text-lg);
  color: var(--primary-dark);
  background-color: rgba(67, 97, 238, 0.05);
  padding: 0.75rem;
  border-radius: 0.25rem;
  margin: 0.5rem 0;
}

/* Print button for results */
.print-results-btn {
  display: inline-block;
  margin-top: 1.5rem;
  padding: 0.75rem 1.25rem;
  background-color: var(--neutral-700);
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-size: var(--text-sm);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.print-results-btn:hover {
  background-color: var(--neutral-800);
}

/* Chart Containers */
.chart-container {
  margin-top: 2rem;
  padding: 1rem;
  background-color: var(--neutral-100);
  border-radius: 0.25rem;
  overflow-x: auto;
  /* Allow horizontal scrolling if needed */
  width: 100%;
  box-sizing: border-box;
}

/* Simple Bar Chart */
.simple-chart {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  gap: 1.5rem;
  /* Reduced gap for mobile */
  height: 250px;
  /* Reduced height for mobile */
  padding: 1rem 0;
  min-width: 300px;
  /* Ensure minimum width for readability */
  margin: 0 auto;
}

.chart-bar {
  width: 60px;
  /* Narrower bars for mobile */
  background-color: var(--primary-color);
  border-radius: 0.25rem 0.25rem 0 0;
  position: relative;
  transition: height 0.5s ease;
}

.chart-bar.invested {
  background-color: var(--invested-color);
}

.chart-bar.returns {
  background-color: var(--returns-color);
}

.bar-label {
  position: absolute;
  bottom: -40px;
  left: 0;
  width: 100%;
  text-align: center;
  font-size: var(--text-sm);
  color: var(--neutral-700);
  word-wrap: break-word;
}

/* Responsive adjustments for charts */
@media (min-width: 768px) {
  .simple-chart {
    height: 300px;
    /* Restore height for larger screens */
    gap: 2rem;
    /* Restore gap for larger screens */
  }

  .chart-bar {
    width: 80px;
    /* Restore width for larger screens */
  }
}

/* Pie Chart */
.pie-chart {
  width: 180px;
  /* Smaller for mobile */
  height: 180px;
  /* Smaller for mobile */
  border-radius: 50%;
  margin: 0 auto 1.5rem;
  position: relative;
}

.pie-label {
  position: absolute;
  font-weight: bold;
  color: white;
  font-size: 0.9rem;
  /* Smaller font for mobile */
}

.principal-label {
  top: 40%;
  left: 30%;
  transform: translate(-50%, -50%);
}

.interest-label {
  top: 60%;
  left: 70%;
  transform: translate(-50%, -50%);
}

.breakdown-legend {
  display: flex;
  flex-direction: column;
  /* Stack vertically on mobile */
  align-items: flex-start;
  gap: 0.75rem;
  margin-top: 1.5rem;
  padding: 0 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.color-box {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  flex-shrink: 0;
}

.principal-color {
  background-color: var(--principal-color);
}

.interest-color {
  background-color: var(--interest-color);
}

/* Responsive adjustments for pie chart */
@media (min-width: 768px) {
  .pie-chart {
    width: 200px;
    /* Restore size for larger screens */
    height: 200px;
    /* Restore size for larger screens */
  }

  .pie-label {
    font-size: 1rem;
    /* Restore font size for larger screens */
  }

  .breakdown-legend {
    flex-direction: row;
    /* Horizontal on larger screens */
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
  }
}

/* BMI Scale */
.bmi-scale {
  margin-top: 2rem;
  width: 100%;
  overflow-x: hidden;
}

.scale-container {
  position: relative;
  height: 30px;
  /* Slightly smaller for mobile */
  background: linear-gradient(to right,
      #3a86ff 0%,
      #3a86ff 16.67%,
      #38b000 16.67%,
      #38b000 50%,
      #ffaa00 50%,
      #ffaa00 75%,
      #d00000 75%,
      #d00000 100%);
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.scale-marker {
  position: absolute;
  top: -10px;
  width: 8px;
  /* Slightly narrower for mobile */
  height: 50px;
  /* Slightly shorter for mobile */
  background-color: var(--neutral-900);
  transform: translateX(-50%);
  transition: left 0.5s ease;
}

.scale-segments {
  display: flex;
  width: 100%;
  margin-top: 0.5rem;
}

.segment {
  flex: 1;
  text-align: center;
  font-size: var(--text-xs);
  padding: 0.25rem 0;
  word-wrap: break-word;
}

.segment.underweight {
  color: #3a86ff;
}

.segment.normal {
  color: #38b000;
}

.segment.overweight {
  color: #ffaa00;
}

.segment.obese {
  color: #d00000;
}

.scale-values {
  display: flex;
  justify-content: space-between;
  margin-top: 0.25rem;
  font-size: var(--text-xs);
  color: var(--neutral-600);
}

/* Responsive adjustments for BMI scale */
@media (min-width: 768px) {
  .scale-container {
    height: 40px;
    /* Restore height for larger screens */
  }

  .scale-marker {
    width: 10px;
    /* Restore width for larger screens */
    height: 60px;
    /* Restore height for larger screens */
  }
}

/* Error Messages */
.error-message {
  color: var(--error-color);
  font-size: var(--text-sm);
  margin-top: 0.25rem;
}

/* Comparison Table */
.comparison-table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  font-size: var(--text-sm);
}

.comparison-table th,
.comparison-table td {
  padding: 0.75rem;
  text-align: left;
  border: 1px solid var(--neutral-300);
}

.comparison-table th {
  background-color: var(--neutral-100);
  font-weight: 600;
  color: var(--neutral-800);
}

.comparison-table tr:nth-child(even) {
  background-color: var(--neutral-50);
}

.comparison-table tr:hover {
  background-color: rgba(67, 97, 238, 0.05);
}

/* Tip Box */
.tip-box {
  background-color: rgba(56, 176, 0, 0.1);
  border-left: 4px solid var(--investment-color);
  padding: 1rem;
  margin: 1.5rem 0;
  border-radius: 0.25rem;
}

.tip-box h4 {
  color: var(--investment-color);
  margin-bottom: 0.5rem;
  font-size: var(--text-base);
}

.tip-box p {
  margin-bottom: 0;
  font-size: var(--text-sm);
}

/* Page Content Styles */
.main-content {
  min-height: 60vh;
  padding: 2rem 0;
}

.content-wrapper {
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 2px solid var(--neutral-200);
}

.page-header h1 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-size: 2.5rem;
}

.page-subtitle {
  font-size: 1.2rem;
  color: var(--neutral-600);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.last-updated {
  font-size: 0.9rem;
  color: var(--neutral-500);
  font-style: italic;
}

.content-body {
  line-height: 1.7;
}

/* Privacy Policy & How It Works Sections */
.privacy-section,
.how-it-works-section {
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid var(--neutral-200);
}

.privacy-section:last-child,
.how-it-works-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.privacy-section h2,
.how-it-works-section h2 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
}

.privacy-section h3,
.how-it-works-section h3 {
  color: var(--neutral-800);
  margin-bottom: 1rem;
  margin-top: 1.5rem;
  font-size: 1.3rem;
}

.highlight-box {
  background-color: rgba(67, 97, 238, 0.05);
  border-left: 4px solid var(--primary-color);
  padding: 1.5rem;
  margin: 2rem 0;
  border-radius: 0.25rem;
}

.highlight-box h3 {
  color: var(--primary-color);
  margin-top: 0;
  margin-bottom: 1rem;
}

.highlight-box ul {
  margin-bottom: 0;
}

/* Formula Categories */
.formula-category {
  margin-bottom: 2.5rem;
  padding: 1.5rem;
  background-color: var(--neutral-50);
  border-radius: 0.5rem;
  border: 1px solid var(--neutral-200);
}

.formula-category h3 {
  color: var(--primary-color);
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.4rem;
}

.formula-category ul {
  margin-bottom: 0;
}

.formula-category li {
  margin-bottom: 0.75rem;
}

.formula-category li:last-child {
  margin-bottom: 0;
}

/* Steps Grid */
.steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin: 2rem 0;
}

.step-item {
  background-color: white;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  border: 1px solid var(--neutral-200);
}

.step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
}

.step-item h3 {
  color: var(--neutral-800);
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.step-item p {
  color: var(--neutral-600);
  line-height: 1.6;
  margin-bottom: 0;
}

/* FAQ Styles */
.faq-category {
  margin-bottom: 3rem;
}

.faq-category h2 {
  color: var(--primary-color);
  margin-bottom: 2rem;
  font-size: 1.8rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-color);
}

.faq-item {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--neutral-200);
}

.faq-item h3 {
  color: var(--neutral-800);
  margin-bottom: 1rem;
  font-size: 1.2rem;
  line-height: 1.4;
}

.faq-item p {
  color: var(--neutral-600);
  line-height: 1.7;
  margin-bottom: 0;
}

.faq-item a {
  color: var(--primary-color);
  text-decoration: underline;
}

.faq-item a:hover {
  color: var(--primary-dark);
}

/* Mobile Responsive Styles for New Pages */
@media (max-width: 767px) {
  .main-content {
    padding: 1rem 0;
  }

  .page-header {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .page-subtitle {
    font-size: 1.1rem;
  }

  .privacy-section,
  .how-it-works-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
  }

  .privacy-section h2,
  .how-it-works-section h2 {
    font-size: 1.5rem;
  }

  .privacy-section h3,
  .how-it-works-section h3 {
    font-size: 1.2rem;
  }

  .highlight-box {
    padding: 1rem;
    margin: 1.5rem 0;
  }

  .formula-category {
    padding: 1rem;
    margin-bottom: 2rem;
  }

  .formula-category h3 {
    font-size: 1.2rem;
  }

  .steps-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin: 1.5rem 0;
  }

  .step-item {
    padding: 1.5rem;
  }

  .step-number {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }

  .step-item h3 {
    font-size: 1.1rem;
  }

  .faq-category {
    margin-bottom: 2rem;
  }

  .faq-category h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .faq-item {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .faq-item h3 {
    font-size: 1.1rem;
  }
}

/* Calculator Cards for Homepage */
.calculator-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 1.25rem;
  /* Slightly smaller padding for mobile */
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 1.5rem;
  /* Add bottom margin for mobile */
}

.calculator-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* Disable hover effect on touch devices */
@media (hover: none) {
  .calculator-card:hover {
    transform: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
}

.card-icon {
  width: 48px;
  /* Smaller for mobile */
  height: 48px;
  /* Smaller for mobile */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.card-title {
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
  /* Slightly smaller for mobile */
}

.card-description {
  color: var(--neutral-600);
  margin-bottom: 1.25rem;
  font-size: 0.9rem;
  /* Slightly smaller for mobile */
}

.card-link {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border-radius: 0.25rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
  -webkit-tap-highlight-color: transparent;
  /* Remove tap highlight on mobile */
}

.card-link:hover {
  background-color: var(--primary-dark);
  text-decoration: none;
}

/* Active state for better mobile feedback */
.card-link:active {
  transform: translateY(1px);
  background-color: var(--primary-dark);
}

/* Responsive adjustments for calculator cards */
@media (min-width: 768px) {
  .calculator-card {
    padding: 1.5rem;
    /* Restore padding for larger screens */
    margin-bottom: 0;
    /* Remove bottom margin for grid layout */
  }

  .card-icon {
    width: 64px;
    /* Restore size for larger screens */
    height: 64px;
    /* Restore size for larger screens */
  }

  .card-title {
    font-size: 1.5rem;
    /* Restore size for larger screens */
  }

  .card-description {
    font-size: 1rem;
    /* Restore size for larger screens */
    margin-bottom: 1.5rem;
    /* Restore margin for larger screens */
  }
}

/* Contact Form Styles */
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--neutral-300);
  border-radius: 0.25rem;
  font-size: var(--text-base);
  transition: border-color 0.2s ease;
  resize: vertical;
  min-height: 120px;
  /* Slightly smaller for mobile */
  box-sizing: border-box;
  -webkit-appearance: none;
  /* Removes default styling on iOS */
  appearance: none;
}

.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

.form-group.has-error input,
.form-group.has-error textarea,
.form-group.has-error select {
  border-color: var(--error-color);
}

.contact-info {
  margin-top: 2rem;
  padding: 1rem;
  background-color: var(--neutral-100);
  border-radius: 0.5rem;
}

.contact-item {
  margin-bottom: 1.5rem;
}

.contact-item h3 {
  margin-bottom: 0.5rem;
  color: var(--primary-color);
  font-size: 1.25rem;
  /* Slightly smaller for mobile */
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 1.25rem;
  /* Slightly smaller padding for mobile */
  border-radius: 0.25rem;
  margin-top: 1rem;
  text-align: center;
  width: 100%;
  box-sizing: border-box;
}

.success-message h3 {
  color: #155724;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  /* Slightly smaller for mobile */
}

.primary-btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-size: var(--text-base);
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 1rem;
  -webkit-tap-highlight-color: transparent;
  /* Remove tap highlight on mobile */
  width: 100%;
  /* Full width on mobile */
  text-align: center;
  box-sizing: border-box;
}

.primary-btn:hover {
  background-color: var(--primary-dark);
  text-decoration: none;
  color: white;
}

/* Active state for better mobile feedback */
.primary-btn:active {
  transform: translateY(1px);
  background-color: var(--primary-dark);
}

/* Related Calculators Section */
.related-calculators {
  background-color: var(--neutral-50);
  padding: 2rem 0;
  margin-top: 3rem;
}

.related-calculators .section-title {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--neutral-800);
  font-size: 1.75rem;
  font-weight: 600;
}

.calculator-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.calculator-grid .calculator-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid var(--neutral-200);
  margin-bottom: 0;
}

.calculator-grid .calculator-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.calculator-grid .calculator-card h3 {
  margin-bottom: 0.75rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary-color);
}

.calculator-grid .calculator-card h3 a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

.calculator-grid .calculator-card h3 a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.calculator-grid .calculator-card p {
  color: var(--neutral-600);
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
}

/* Responsive adjustments for related calculators */
@media (max-width: 768px) {
  .related-calculators {
    padding: 1.5rem 0;
    margin-top: 2rem;
  }

  .related-calculators .section-title {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .calculator-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0 0.75rem;
  }

  .calculator-grid .calculator-card {
    padding: 1.25rem;
  }

  .calculator-grid .calculator-card h3 {
    font-size: 1.1rem;
  }
}

/* Disable hover effect on touch devices */
@media (hover: none) {
  .calculator-grid .calculator-card:hover {
    transform: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
}

/* Blog Styles */
.blog-header {
  text-align: center;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid var(--neutral-200);
}

.blog-header h1 {
  color: var(--neutral-900);
  margin-bottom: 1rem;
}

.blog-subtitle {
  font-size: 1.2rem;
  color: var(--neutral-600);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.featured-articles,
.latest-articles,
.blog-categories,
.category-section,
.all-articles {
  margin-bottom: 4rem;
}

.featured-articles h2,
.latest-articles h2,
.blog-categories h2,
.category-section h2,
.all-articles h2 {
  color: var(--neutral-900);
  margin-bottom: 2rem;
  font-size: 2rem;
  text-align: center;
}

.blog-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.blog-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.blog-card.featured {
  border: 2px solid var(--primary-color);
}

.blog-card-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.blog-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.blog-card:hover .blog-card-image img {
  transform: scale(1.05);
}

.blog-card-content {
  padding: 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.blog-category {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background-color: var(--primary-color);
  color: white;
  font-size: 0.8rem;
  font-weight: 500;
  border-radius: 1rem;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.blog-card h3 {
  margin-bottom: 1rem;
  font-size: 1.25rem;
  line-height: 1.4;
}

.blog-card h3 a {
  color: var(--neutral-900);
  text-decoration: none;
  transition: color 0.2s ease;
}

.blog-card h3 a:hover {
  color: var(--primary-color);
  text-decoration: none;
}

.blog-card p {
  color: var(--neutral-600);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

.blog-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.9rem;
  color: var(--neutral-500);
  margin-top: auto;
}

.blog-date,
.blog-read-time {
  display: flex;
  align-items: center;
}

.category-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.category-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.category-card h3 {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.category-card h3 a {
  color: var(--neutral-900);
  text-decoration: none;
  transition: color 0.2s ease;
}

.category-card h3 a:hover {
  color: var(--primary-color);
  text-decoration: none;
}

.category-card p {
  color: var(--neutral-600);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

.article-count {
  font-size: 0.9rem;
  color: var(--neutral-500);
  font-weight: 500;
  margin-top: auto;
}

.category-section {
  padding: 2rem 0;
  border-bottom: 1px solid var(--neutral-200);
}

.category-section:last-of-type {
  border-bottom: none;
}

/* FAQ and content sections */
.faq-item {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--neutral-200);
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-item h3 {
  margin-bottom: 0.75rem;
  color: var(--neutral-800);
  font-size: 1.1rem;
  /* Smaller for mobile */
}

/* Responsive adjustments for contact form and content */
@media (min-width: 768px) {
  .form-group textarea {
    min-height: 150px;
    /* Restore height for larger screens */
  }

  .contact-item h3 {
    font-size: 1.5rem;
    /* Restore size for larger screens */
  }

  .success-message {
    padding: 1.5rem;
    /* Restore padding for larger screens */
  }

  .success-message h3 {
    font-size: 1.5rem;
    /* Restore size for larger screens */
  }

  .primary-btn {
    width: auto;
    /* Auto width on larger screens */
  }

  .faq-item h3 {
    font-size: 1.25rem;
    /* Restore size for larger screens */
  }

  /* Blog responsive styles for larger screens */
  .blog-header {
    margin-bottom: 4rem;
    padding-bottom: 3rem;
  }

  .blog-subtitle {
    font-size: 1.3rem;
  }

  .category-icon {
    width: 80px;
    height: 80px;
  }

  .blog-card h3 {
    font-size: 1.4rem;
  }

  .category-card h3 {
    font-size: 1.4rem;
  }
}

/* Category Header Styles */
.category-header {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  margin-bottom: 3rem;
  padding: 2rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--neutral-200);
}

.category-info {
  flex: 1;
}

.category-description {
  color: var(--neutral-600);
  font-size: var(--text-lg);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.category-extended-description {
  color: var(--neutral-600);
  font-size: var(--text-base);
  line-height: 1.7;
  margin-bottom: 0;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--neutral-200);
}

/* Responsive adjustments for category header */
@media (max-width: 767px) {
  .category-header {
    flex-direction: column;
    gap: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .category-description {
    font-size: var(--text-base);
  }

  .category-extended-description {
    font-size: var(--text-sm);
    margin-top: 0.75rem;
    padding-top: 0.75rem;
  }
}

/* Blog mobile responsive styles */
@media (max-width: 767px) {
  .blog-header {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
  }

  .blog-header h1 {
    font-size: 2rem;
  }

  .blog-subtitle {
    font-size: 1rem;
  }

  .featured-articles,
  .latest-articles,
  .blog-categories,
  .category-section,
  .all-articles {
    margin-bottom: 2.5rem;
  }

  .featured-articles h2,
  .latest-articles h2,
  .blog-categories h2,
  .category-section h2,
  .all-articles h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .blog-card-content {
    padding: 1rem;
  }

  .blog-card h3 {
    font-size: 1.1rem;
  }

  .category-card {
    padding: 1.5rem;
  }

  .category-icon {
    width: 48px;
    height: 48px;
    margin-bottom: 1rem;
  }

  .category-card h3 {
    font-size: 1.1rem;
  }

  .category-section {
    padding: 1.5rem 0;
  }

  .blog-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}