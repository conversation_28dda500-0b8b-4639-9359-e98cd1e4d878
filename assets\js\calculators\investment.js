/**
 * Investment Calculator Scripts
 */

document.addEventListener("DOMContentLoaded", function () {
  // Initialize SIP Calculator if it exists on the page
  const sipCalculatorForm = document.getElementById("sip-calculator-form");
  if (sipCalculatorForm) {
    initSIPCalculator();
  }

  // Initialize Compound Interest Calculator if it exists on the page
  const compoundInterestForm = document.getElementById(
    "compound-interest-form",
  );
  if (compoundInterestForm) {
    initCompoundInterestCalculator();
  }

  // Initialize Lump Sum Calculator if it exists on the page
  const lumpSumForm = document.getElementById("lump-sum-form");
  if (lumpSumForm) {
    initLumpSumCalculator();
  }

  // Initialize Investment Goal Calculator if it exists on the page
  const goalCalculatorForm = document.getElementById("goal-calculator-form");
  if (goalCalculatorForm) {
    initGoalCalculator();
  }
});

/**
 * Initialize SIP Calculator
 */
function initSIPCalculator() {
  // Get form elements
  const form = document.getElementById("sip-calculator-form");
  const monthlyInvestment = document.getElementById("monthly-investment");
  const investmentPeriod = document.getElementById("investment-period");
  const expectedReturn = document.getElementById("expected-return");
  const results = document.getElementById("sip-results");

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("sip");
  if (savedValues) {
    monthlyInvestment.value = savedValues.monthlyInvestment;
    investmentPeriod.value = savedValues.investmentPeriod;
    expectedReturn.value = savedValues.expectedReturn;
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const monthlyInvestmentValue = parseFloat(monthlyInvestment.value);
    const investmentPeriodValue = parseInt(investmentPeriod.value);
    const expectedReturnValue = parseFloat(expectedReturn.value);

    // Validate inputs
    const investmentValidation = calculatorUtils.validateNumericInput(
      monthlyInvestmentValue,
      100,
      Number.MAX_SAFE_INTEGER,
      "Please enter a valid amount (minimum ₹100)",
    );

    if (!investmentValidation.valid) {
      calculatorUtils.showError(
        monthlyInvestment,
        investmentValidation.message,
      );
      return;
    }

    const periodValidation = calculatorUtils.validateNumericInput(
      investmentPeriodValue,
      1,
      50,
      "Please enter a valid period between 1 and 50 years",
    );

    if (!periodValidation.valid) {
      calculatorUtils.showError(investmentPeriod, periodValidation.message);
      return;
    }

    const returnValidation = calculatorUtils.validateNumericInput(
      expectedReturnValue,
      1,
      30,
      "Please enter a valid return rate between 1% and 30%",
    );

    if (!returnValidation.valid) {
      calculatorUtils.showError(expectedReturn, returnValidation.message);
      return;
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("sip", {
      monthlyInvestment: monthlyInvestmentValue,
      investmentPeriod: investmentPeriodValue,
      expectedReturn: expectedReturnValue,
    });

    // Calculate SIP returns
    const monthlyRate = expectedReturnValue / (12 * 100);
    const months = investmentPeriodValue * 12;
    const totalInvested = monthlyInvestmentValue * months;

    // Formula: P × ({[1 + i]^n - 1} / i) × (1 + i)
    // Where P is monthly investment, i is monthly interest rate, n is number of months
    const totalValue =
      monthlyInvestmentValue *
      ((Math.pow(1 + monthlyRate, months) - 1) / monthlyRate) *
      (1 + monthlyRate);

    const estimatedReturns = totalValue - totalInvested;

    // Round values to 2 decimal places
    const roundedTotalValue = calculatorUtils.round(totalValue, 2);
    const roundedEstimatedReturns = calculatorUtils.round(estimatedReturns, 2);

    // Display results
    document.getElementById("total-invested").textContent =
      calculatorUtils.formatCurrency(totalInvested);
    document.getElementById("estimated-returns").textContent =
      calculatorUtils.formatCurrency(roundedEstimatedReturns);
    document.getElementById("total-value").textContent =
      calculatorUtils.formatCurrency(roundedTotalValue);
    results.style.display = "block";

    // Generate and display chart
    generateSipChart(
      monthlyInvestmentValue,
      investmentPeriodValue,
      expectedReturnValue,
      totalInvested,
      roundedTotalValue,
    );

    // Save calculation to history
    storageManager.saveCalculationHistory("sip", {
      monthlyInvestment: monthlyInvestmentValue,
      investmentPeriod: investmentPeriodValue,
      expectedReturn: expectedReturnValue,
      totalInvested: totalInvested,
      estimatedReturns: roundedEstimatedReturns,
      totalValue: roundedTotalValue,
    });

    // Show in-article ad after calculation
    const inArticleAd = document.getElementById(
      "ad-between-calculator-results",
    );
    if (inArticleAd) {
      inArticleAd.style.display = "block";
    }
  });
}

/**
 * Generate SIP growth chart
 */
function generateSipChart(
  monthlyInvestment,
  years,
  rate,
  totalInvested,
  totalValue,
) {
  const chartContainer = document.getElementById("sip-chart-container");
  if (!chartContainer) return;

  chartContainer.innerHTML = ""; // Clear previous chart

  // Create a simple bar chart using HTML/CSS
  const chartEl = document.createElement("div");
  chartEl.className = "simple-chart";

  const investedBar = document.createElement("div");
  investedBar.className = "chart-bar invested";
  investedBar.style.height = "100px"; // Fixed height for invested amount
  investedBar.innerHTML = `<span class="bar-label">Invested<br>${calculatorUtils.formatCurrency(
    totalInvested,
  )}</span>`;

  const returnsBar = document.createElement("div");
  returnsBar.className = "chart-bar returns";
  // Calculate proportional height for returns
  const returnsHeight = (totalValue / totalInvested) * 100;
  returnsBar.style.height = `${Math.min(returnsHeight, 300)}px`; // Cap at 300px
  returnsBar.innerHTML = `<span class="bar-label">Total Value<br>${calculatorUtils.formatCurrency(
    totalValue,
  )}</span>`;

  chartEl.appendChild(investedBar);
  chartEl.appendChild(returnsBar);
  chartContainer.appendChild(chartEl);
}

/**
 * Initialize Compound Interest Calculator
 */
function initCompoundInterestCalculator() {
  // Get form elements
  const form = document.getElementById("compound-interest-form");
  const principalAmount = document.getElementById("principal-amount");
  const interestRate = document.getElementById("interest-rate");
  const timePeriod = document.getElementById("time-period");
  const compoundingFrequency = document.getElementById("compounding-frequency");
  const results = document.getElementById("compound-interest-results");

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("compound-interest");
  if (savedValues) {
    principalAmount.value = savedValues.principalAmount;
    interestRate.value = savedValues.interestRate;
    timePeriod.value = savedValues.timePeriod;
    compoundingFrequency.value = savedValues.compoundingFrequency;
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const principalValue = parseFloat(principalAmount.value);
    const rateValue = parseFloat(interestRate.value);
    const timeValue = parseInt(timePeriod.value);
    const frequencyValue = parseInt(compoundingFrequency.value);

    // Validate inputs - Principal amount validation removed

    const rateValidation = calculatorUtils.validateNumericInput(
      rateValue,
      0.1,
      50,
      "Please enter a valid interest rate between 0.1% and 50%",
    );

    if (!rateValidation.valid) {
      calculatorUtils.showError(interestRate, rateValidation.message);
      return;
    }

    const timeValidation = calculatorUtils.validateNumericInput(
      timeValue,
      1,
      50,
      "Please enter a valid time period between 1 and 50 years",
    );

    if (!timeValidation.valid) {
      calculatorUtils.showError(timePeriod, timeValidation.message);
      return;
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("compound-interest", {
      principalAmount: principalValue,
      interestRate: rateValue,
      timePeriod: timeValue,
      compoundingFrequency: frequencyValue,
    });

    // Calculate compound interest
    // Formula: P(1 + r/n)^(nt)
    // Where P is principal, r is rate (decimal), n is compounding frequency, t is time in years
    const rate = rateValue / 100;
    const finalAmount =
      principalValue *
      Math.pow(1 + rate / frequencyValue, frequencyValue * timeValue);
    const interestEarned = finalAmount - principalValue;

    // Round values to 2 decimal places
    const roundedFinalAmount = calculatorUtils.round(finalAmount, 2);
    const roundedInterestEarned = calculatorUtils.round(interestEarned, 2);

    // Display results
    document.getElementById("result-principal").textContent =
      calculatorUtils.formatCurrency(principalValue);
    document.getElementById("interest-earned").textContent =
      calculatorUtils.formatCurrency(roundedInterestEarned);
    document.getElementById("final-amount").textContent =
      calculatorUtils.formatCurrency(roundedFinalAmount);
    results.style.display = "block";

    // Generate and display chart
    generateCompoundChart(
      principalValue,
      roundedFinalAmount,
      timeValue,
      frequencyValue,
      rateValue,
    );

    // Save calculation to history
    storageManager.saveCalculationHistory("compound-interest", {
      principalAmount: principalValue,
      interestRate: rateValue,
      timePeriod: timeValue,
      compoundingFrequency: frequencyValue,
      interestEarned: roundedInterestEarned,
      finalAmount: roundedFinalAmount,
    });

    // Show in-article ad after calculation
    const inArticleAd = document.getElementById(
      "ad-between-calculator-results",
    );
    if (inArticleAd) {
      inArticleAd.style.display = "block";
    }
  });
}

/**
 * Generate compound interest growth chart
 */
function generateCompoundChart(principal, finalAmount, years, frequency, rate) {
  const chartContainer = document.getElementById("compound-chart-container");
  if (!chartContainer) return;

  chartContainer.innerHTML = ""; // Clear previous chart

  // Create a simple growth chart using HTML/CSS
  const chartEl = document.createElement("div");
  chartEl.className = "growth-chart";

  // Create year markers and value points
  for (let i = 0; i <= years; i += Math.max(1, Math.floor(years / 5))) {
    // Calculate the value at this year using the same compound interest formula
    const yearValue =
      i === 0
        ? principal
        : principal * Math.pow(1 + rate / 100 / frequency, frequency * i);

    // Create a point on the chart
    const point = document.createElement("div");
    point.className = "chart-point";
    // Position horizontally based on year
    point.style.left = `${(i / years) * 100}%`;
    // Position vertically based on value (inverted, as 0 is at the top in CSS)
    const verticalPosition = 100 - (yearValue / finalAmount) * 100;
    point.style.bottom = `${verticalPosition}%`;

    // Add label
    point.innerHTML = `<span class="point-label">Year ${i}<br>${calculatorUtils.formatCurrency(
      yearValue,
    )}</span>`;

    chartEl.appendChild(point);
  }

  chartContainer.appendChild(chartEl);
}

/**
 * Initialize Lump Sum Calculator
 */
function initLumpSumCalculator() {
  // Get form elements
  const form = document.getElementById("lump-sum-form");
  const investmentAmount = document.getElementById("investment-amount");
  const investmentPeriod = document.getElementById("investment-period");
  const expectedReturn = document.getElementById("expected-return");
  const results = document.getElementById("lump-sum-results");

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("lump-sum");
  if (savedValues) {
    investmentAmount.value = savedValues.investmentAmount;
    investmentPeriod.value = savedValues.investmentPeriod;
    expectedReturn.value = savedValues.expectedReturn;
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const investmentAmountValue = parseFloat(investmentAmount.value);
    const investmentPeriodValue = parseInt(investmentPeriod.value);
    const expectedReturnValue = parseFloat(expectedReturn.value);

    // Validate inputs
    const amountValidation = calculatorUtils.validateNumericInput(
      investmentAmountValue,
      1000,
      Number.MAX_SAFE_INTEGER,
      "Please enter a valid amount (minimum ₹1,000)",
    );

    if (!amountValidation.valid) {
      calculatorUtils.showError(investmentAmount, amountValidation.message);
      return;
    }

    const periodValidation = calculatorUtils.validateNumericInput(
      investmentPeriodValue,
      1,
      50,
      "Please enter a valid period (1-50 years)",
    );

    if (!periodValidation.valid) {
      calculatorUtils.showError(investmentPeriod, periodValidation.message);
      return;
    }

    const returnValidation = calculatorUtils.validateNumericInput(
      expectedReturnValue,
      1,
      30,
      "Please enter a valid return rate (1-30%)",
    );

    if (!returnValidation.valid) {
      calculatorUtils.showError(expectedReturn, returnValidation.message);
      return;
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("lump-sum", {
      investmentAmount: investmentAmountValue,
      investmentPeriod: investmentPeriodValue,
      expectedReturn: expectedReturnValue,
    });

    // Calculate lump sum investment returns
    // Formula: A = P(1 + r)^t
    // Where A is final amount, P is principal, r is rate, t is time
    const rate = expectedReturnValue / 100;
    const finalAmount =
      investmentAmountValue * Math.pow(1 + rate, investmentPeriodValue);
    const estimatedReturns = finalAmount - investmentAmountValue;

    // Round values to 2 decimal places
    const roundedFinalAmount = calculatorUtils.round(finalAmount, 2);
    const roundedEstimatedReturns = calculatorUtils.round(estimatedReturns, 2);

    // Display results
    document.getElementById("invested-amount").textContent =
      calculatorUtils.formatCurrency(investmentAmountValue);
    document.getElementById("estimated-returns").textContent =
      calculatorUtils.formatCurrency(roundedEstimatedReturns);
    document.getElementById("total-value").textContent =
      calculatorUtils.formatCurrency(roundedFinalAmount);
    results.style.display = "block";

    // Generate and display chart
    generateLumpSumChart(
      investmentAmountValue,
      roundedEstimatedReturns,
      roundedFinalAmount,
    );

    // Save calculation to history
    storageManager.saveCalculationHistory("lump-sum", {
      investmentAmount: investmentAmountValue,
      investmentPeriod: investmentPeriodValue,
      expectedReturn: expectedReturnValue,
      finalAmount: roundedFinalAmount,
      estimatedReturns: roundedEstimatedReturns,
    });

    // Show in-article ad after calculation
    const inArticleAd = document.getElementById(
      "ad-between-calculator-results",
    );
    if (inArticleAd) {
      inArticleAd.style.display = "block";
    }
  });
}

/**
 * Generate chart for Lump Sum Calculator
 */
function generateLumpSumChart(investmentAmount, estimatedReturns, totalValue) {
  const chartContainer = document.getElementById("lump-sum-chart-container");

  // Clear previous chart if any
  chartContainer.innerHTML = "";

  // Create chart elements
  const chartEl = document.createElement("div");
  chartEl.className = "pie-chart";

  // Create principal slice
  const principalSlice = document.createElement("div");
  principalSlice.className = "chart-slice principal-slice";
  principalSlice.style.width = (investmentAmount / totalValue) * 100 + "%";

  // Create returns slice
  const returnsSlice = document.createElement("div");
  returnsSlice.className = "chart-slice returns-slice";
  returnsSlice.style.width = (estimatedReturns / totalValue) * 100 + "%";

  // Create legend
  const legend = document.createElement("div");
  legend.className = "chart-legend";

  const principalLegend = document.createElement("div");
  principalLegend.className = "legend-item";
  principalLegend.innerHTML =
    '<span class="legend-color principal-color"></span> Principal: ' +
    calculatorUtils.formatCurrency(investmentAmount) +
    " (" +
    calculatorUtils.round((investmentAmount / totalValue) * 100, 1) +
    "%)";

  const returnsLegend = document.createElement("div");
  returnsLegend.className = "legend-item";
  returnsLegend.innerHTML =
    '<span class="legend-color returns-color"></span> Returns: ' +
    calculatorUtils.formatCurrency(estimatedReturns) +
    " (" +
    calculatorUtils.round((estimatedReturns / totalValue) * 100, 1) +
    "%)";

  // Assemble chart
  chartEl.appendChild(principalSlice);
  chartEl.appendChild(returnsSlice);
  legend.appendChild(principalLegend);
  legend.appendChild(returnsLegend);

  chartContainer.appendChild(chartEl);
  chartContainer.appendChild(legend);
}

/**
 * Initialize Investment Goal Calculator
 */
function initGoalCalculator() {
  // Get form elements
  const form = document.getElementById("goal-calculator-form");
  const targetAmount = document.getElementById("target-amount");
  const timeHorizon = document.getElementById("time-horizon");
  const expectedReturn = document.getElementById("expected-return");
  const investmentFrequency = document.getElementById("investment-frequency");
  const results = document.getElementById("goal-results");

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("goal-calculator");
  if (savedValues) {
    targetAmount.value = savedValues.targetAmount;
    timeHorizon.value = savedValues.timeHorizon;
    expectedReturn.value = savedValues.expectedReturn;
    investmentFrequency.value = savedValues.investmentFrequency;
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const targetAmountValue = parseFloat(targetAmount.value);
    const timeHorizonValue = parseInt(timeHorizon.value);
    const expectedReturnValue = parseFloat(expectedReturn.value);
    const investmentFrequencyValue = parseInt(investmentFrequency.value);

    // Validate inputs
    const targetValidation = calculatorUtils.validateNumericInput(
      targetAmountValue,
      10000,
      Number.MAX_SAFE_INTEGER,
      "Please enter a valid target amount (minimum ₹10,000)",
    );

    if (!targetValidation.valid) {
      calculatorUtils.showError(targetAmount, targetValidation.message);
      return;
    }

    const timeValidation = calculatorUtils.validateNumericInput(
      timeHorizonValue,
      1,
      50,
      "Please enter a valid time horizon (1-50 years)",
    );

    if (!timeValidation.valid) {
      calculatorUtils.showError(timeHorizon, timeValidation.message);
      return;
    }

    const returnValidation = calculatorUtils.validateNumericInput(
      expectedReturnValue,
      1,
      30,
      "Please enter a valid return rate (1-30%)",
    );

    if (!returnValidation.valid) {
      calculatorUtils.showError(expectedReturn, returnValidation.message);
      return;
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("goal-calculator", {
      targetAmount: targetAmountValue,
      timeHorizon: timeHorizonValue,
      expectedReturn: expectedReturnValue,
      investmentFrequency: investmentFrequencyValue,
    });

    // Calculate required investment amount
    // Formula: PMT = FV / {[(1 + r)^n - 1] / r}
    // Where PMT is periodic payment, FV is future value, r is periodic rate, n is number of periods
    const periodicRate = expectedReturnValue / 100 / investmentFrequencyValue;
    const totalPeriods = timeHorizonValue * investmentFrequencyValue;
    const requiredInvestment =
      targetAmountValue /
      ((Math.pow(1 + periodicRate, totalPeriods) - 1) / periodicRate);

    // Calculate total investment and returns
    const totalInvestment = requiredInvestment * totalPeriods;
    const estimatedReturns = targetAmountValue - totalInvestment;

    // Round values to 2 decimal places
    const roundedRequiredInvestment = calculatorUtils.round(
      requiredInvestment,
      2,
    );
    const roundedTotalInvestment = calculatorUtils.round(totalInvestment, 2);
    const roundedEstimatedReturns = calculatorUtils.round(estimatedReturns, 2);

    // Display results
    document.getElementById("result-target-amount").textContent =
      calculatorUtils.formatCurrency(targetAmountValue);
    document.getElementById("result-time-horizon").textContent =
      timeHorizonValue + " years";
    document.getElementById("required-monthly-investment").textContent =
      investmentFrequencyValue === 12
        ? calculatorUtils.formatCurrency(roundedRequiredInvestment) +
          " per month"
        : calculatorUtils.formatCurrency(roundedRequiredInvestment) +
          " per year";
    document.getElementById("total-investment").textContent =
      calculatorUtils.formatCurrency(roundedTotalInvestment);
    document.getElementById("estimated-returns").textContent =
      calculatorUtils.formatCurrency(roundedEstimatedReturns);
    results.style.display = "block";

    // Generate and display chart
    generateGoalChart(
      roundedTotalInvestment,
      roundedEstimatedReturns,
      targetAmountValue,
    );

    // Save calculation to history
    storageManager.saveCalculationHistory("goal-calculator", {
      targetAmount: targetAmountValue,
      timeHorizon: timeHorizonValue,
      expectedReturn: expectedReturnValue,
      investmentFrequency: investmentFrequencyValue,
      requiredInvestment: roundedRequiredInvestment,
      totalInvestment: roundedTotalInvestment,
      estimatedReturns: roundedEstimatedReturns,
    });

    // Show in-article ad after calculation
    const inArticleAd = document.getElementById(
      "ad-between-calculator-results",
    );
    if (inArticleAd) {
      inArticleAd.style.display = "block";
    }
  });
}

/**
 * Generate chart for Investment Goal Calculator
 */
function generateGoalChart(totalInvestment, estimatedReturns, targetAmount) {
  const chartContainer = document.getElementById("goal-chart-container");

  // Clear previous chart if any
  chartContainer.innerHTML = "";

  // Create chart elements
  const chartEl = document.createElement("div");
  chartEl.className = "pie-chart";

  // Create investment slice
  const investmentSlice = document.createElement("div");
  investmentSlice.className = "chart-slice principal-slice";
  investmentSlice.style.width = (totalInvestment / targetAmount) * 100 + "%";

  // Create returns slice
  const returnsSlice = document.createElement("div");
  returnsSlice.className = "chart-slice returns-slice";
  returnsSlice.style.width = (estimatedReturns / targetAmount) * 100 + "%";

  // Create legend
  const legend = document.createElement("div");
  legend.className = "chart-legend";

  const investmentLegend = document.createElement("div");
  investmentLegend.className = "legend-item";
  investmentLegend.innerHTML =
    '<span class="legend-color principal-color"></span> Total Investment: ' +
    calculatorUtils.formatCurrency(totalInvestment) +
    " (" +
    calculatorUtils.round((totalInvestment / targetAmount) * 100, 1) +
    "%)";

  const returnsLegend = document.createElement("div");
  returnsLegend.className = "legend-item";
  returnsLegend.innerHTML =
    '<span class="legend-color returns-color"></span> Returns: ' +
    calculatorUtils.formatCurrency(estimatedReturns) +
    " (" +
    calculatorUtils.round((estimatedReturns / targetAmount) * 100, 1) +
    "%)";

  // Assemble chart
  chartEl.appendChild(investmentSlice);
  chartEl.appendChild(returnsSlice);
  legend.appendChild(investmentLegend);
  legend.appendChild(returnsLegend);

  chartContainer.appendChild(chartEl);
  chartContainer.appendChild(legend);
}
